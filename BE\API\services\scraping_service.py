"""
Scraping Service

This service provides high-level scraping operations for stories and chapters.
Integrates with the real MetruyenScraper for actual web scraping functionality.
"""

import asyncio
import sys
import os
from pathlib import Path
from typing import Dict, List, Optional, Any
from datetime import datetime

from utils.logging_config import LoggerMixin
from middleware.error_handling import Scraping<PERSON>rror
from config import get_settings


class ScrapingService(LoggerMixin):
    """Service for web scraping operations using real MetruyenScraper"""

    def __init__(self):
        self.settings = get_settings()
        self._initialized = False
        self._scraper = None
        # In Docker, Scraper is at /app/Scraper
        # In development, it's at ../Scraper relative to API directory
        if os.path.exists("/app/Scraper"):
            self._scraper_path = Path("/app/Scraper")
        else:
            self._scraper_path = Path(__file__).parent.parent.parent / "Scraper"

    async def _initialize(self):
        """Initialize scraper components"""
        if self._initialized:
            return

        try:
            self.log_info("Initializing real scraping service...")

            # Fix for Python 3.13 + Playwright on Windows
            import asyncio
            import platform
            if platform.system() == "Windows" and hasattr(asyncio, 'WindowsProactorEventLoopPolicy'):
                try:
                    asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())
                    self.log_info("Set Windows ProactorEventLoopPolicy for Playwright compatibility")
                except Exception as policy_error:
                    self.log_warning(f"Could not set event loop policy: {policy_error}")

            # Add scraper path to sys.path
            if str(self._scraper_path) not in sys.path:
                sys.path.insert(0, str(self._scraper_path))

            # Import and initialize the real scraper
            from src.metruyenscraper import MetruyenScraper

            # Initialize scraper with config
            config_path = self._scraper_path / "config.yaml"
            self._scraper = MetruyenScraper(str(config_path))
            await self._scraper.start()

            self._initialized = True
            self.log_info("✅ Real scraping service initialized successfully")

        except Exception as e:
            error_msg = f"Failed to initialize scraping service: {str(e) if e else 'Unknown error'}"
            self.log_error(error_msg)
            self.log_error(f"Exception type: {type(e).__name__}")
            import traceback
            self.log_error(f"Traceback: {traceback.format_exc()}")
            raise ScrapingError(f"Scraping service initialization failed: {str(e) if e else 'Unknown error'}")

    async def _cleanup(self):
        """Cleanup scraper resources"""
        try:
            if self._scraper:
                await self._scraper.close()
                self._scraper = None
            self._initialized = False
            self.log_info("✅ Scraping service cleaned up")

        except Exception as e:
            self.log_error(f"Error during scraping service cleanup: {e}")
    
    async def scrape_story_info(
        self,
        story_url: str,
        max_pages: int = 10,
    ) -> Dict[str, Any]:
        """
        Scrape story information and chapter list using real scraper

        Args:
            story_url: URL of the story page
            max_pages: Maximum number of pages to scrape for chapters (default: 10)

        Returns:
            Dictionary containing story information and chapters
        """
        await self._initialize()

        try:
            self.log_info(f"Scraping story info from: {story_url}")

            # First, get the story page to extract basic info and chapter list
            story_data = await self._scraper.scrape_url(story_url, "webtruyen")

            if not story_data:
                raise ScrapingError("Failed to scrape story page", url=story_url)

            # Extract basic story information
            story_title = story_data.get("title", "Unknown Title")
            story_metadata = story_data.get("metadata", {})

            result = {
                "url": story_url,
                "title": story_title,
                "timestamp": datetime.utcnow().isoformat(),
                "metadata": {
                    "scraped_at": datetime.utcnow().isoformat(),
                    "source_website": "webtruyen",
                    "status": "ongoing",
                    "author": story_metadata.get("author", "Unknown"),
                    "description": story_metadata.get("description", "")
                }
            }

            # Always include chapters (removed include_chapters parameter)
            # Get comprehensive chapter list using pagination (URLs only, no content)
            try:
                # Use the scraper engine's scrape_chapters method with max_pages limit
                chapter_list_data = await self._scraper.scraper_engine.scrape_chapters_with_page_limit(
                    story_url, "webtruyen", max_pages
                )

                if chapter_list_data and "chapters" in chapter_list_data:
                    chapters = []
                    chapter_list = chapter_list_data["chapters"]
                    total_chapters_found = chapter_list_data.get("total_chapters", len(chapter_list))

                    # No need to limit chapters since we're limiting pages instead
                    for i, chapter_info in enumerate(chapter_list):
                        chapters.append({
                            "title": chapter_info.get("title", f"Chapter {i+1}"),
                            "url": chapter_info.get("url", ""),
                            "chapter_number": i + 1
                        })

                    result["chapters"] = chapters
                    result["metadata"]["total_chapters"] = total_chapters_found
                    result["metadata"]["included_chapters"] = len(chapters)
                    result["metadata"]["all_chapter_urls"] = [ch.get("url") for ch in chapter_list]
                    result["metadata"]["max_pages_scraped"] = max_pages

                    # Add pagination metadata if available
                    if "total_pages_crawled" in chapter_list_data:
                        result["metadata"]["total_pages_crawled"] = chapter_list_data["total_pages_crawled"]
                    if "total_pages_available" in chapter_list_data:
                        result["metadata"]["total_pages_available"] = chapter_list_data["total_pages_available"]
                else:
                    # Fallback: no chapters found
                    result["chapters"] = []
                    result["metadata"]["total_chapters"] = 0
                    result["metadata"]["included_chapters"] = 0

            except Exception as chapter_error:
                self.log_error(f"Failed to get chapter list: {chapter_error}")
                # Fallback: no chapters
                result["chapters"] = []
                result["metadata"]["total_chapters"] = 0
                result["metadata"]["included_chapters"] = 0

            self.log_info(f"Successfully scraped story info: {result['title']} ({len(result.get('chapters', []))} chapters)")
            return result

        except Exception as e:
            self.log_error(f"Error scraping story info from {story_url}: {e}")
            raise ScrapingError(f"Failed to scrape story information: {e}", url=story_url)
    
    async def scrape_chapter_content(self, chapter_url: str) -> Dict[str, Any]:
        """
        Scrape content from a single chapter using real scraper

        Args:
            chapter_url: URL of the chapter page

        Returns:
            Dictionary containing chapter content and metadata
        """
        await self._initialize()

        try:
            self.log_info(f"Scraping chapter content from: {chapter_url}")
            start_time = datetime.utcnow()

            # Use real scraper to get chapter content
            chapter_data = await self._scraper.scrape_url(chapter_url, "webtruyen")

            if not chapter_data:
                raise ScrapingError("Failed to scrape chapter content", url=chapter_url)

            # Calculate scraping duration
            end_time = datetime.utcnow()
            duration = (end_time - start_time).total_seconds()

            # Extract content and metadata
            content = chapter_data.get("content", "")
            title = chapter_data.get("title", "Unknown Chapter")
            is_locked = chapter_data.get("is_locked", False)
            navigation = chapter_data.get("navigation", {})

            # Build result
            result = {
                "url": chapter_url,
                "title": title,
                "content": content,
                "is_locked": is_locked,
                "scraping_duration": duration,
                "timestamp": end_time.isoformat(),
                "metadata": {
                    "scraped_at": end_time.isoformat(),
                    "word_count": len(content.split()) if content else 0,
                    "character_count": len(content) if content else 0,
                    "source_url": chapter_url,
                    "navigation": navigation,
                    "scraping_metadata": chapter_data.get("metadata", {})
                }
            }

            self.log_info(f"Successfully scraped chapter: {title} ({result['metadata']['word_count']} words)")
            return result

        except Exception as e:
            self.log_error(f"Error scraping chapter content from {chapter_url}: {e}")
            raise ScrapingError(f"Failed to scrape chapter content: {e}", url=chapter_url)
    
    async def scrape_multiple_chapters(
        self,
        chapter_urls: List[str],
        max_concurrent: int = 3,
        rate_limit_delay: float = 2.0
    ) -> List[Dict[str, Any]]:
        """
        Scrape content from multiple chapters concurrently using real scraper

        Args:
            chapter_urls: List of chapter URLs to scrape
            max_concurrent: Maximum concurrent scraping operations
            rate_limit_delay: Delay between requests in seconds

        Returns:
            List of chapter data dictionaries
        """
        await self._initialize()

        try:
            self.log_info(f"Starting batch scraping of {len(chapter_urls)} chapters")

            # Scrape chapters one by one with rate limiting
            results = []
            failed_urls = []

            for i, chapter_url in enumerate(chapter_urls):
                try:
                    self.log_info(f"Scraping chapter {i+1}/{len(chapter_urls)}: {chapter_url}")

                    # Add rate limiting delay
                    if i > 0:
                        await asyncio.sleep(rate_limit_delay)

                    # Scrape individual chapter
                    chapter_data = await self._scraper.scrape_url(chapter_url, "webtruyen")

                    if chapter_data:
                        content = chapter_data.get("content", "")
                        title = chapter_data.get("title", "Unknown Chapter")
                        url = chapter_data.get("url", chapter_url)
                        is_locked = chapter_data.get("is_locked", False)
                        navigation = chapter_data.get("navigation", {})

                        result = {
                            "url": url,
                            "title": title,
                            "content": content,
                            "is_locked": is_locked,
                            "scraping_duration": 0,  # Not tracked in batch mode
                            "timestamp": datetime.utcnow().isoformat(),
                            "metadata": {
                                "scraped_at": datetime.utcnow().isoformat(),
                                "word_count": len(content.split()) if content else 0,
                                "character_count": len(content) if content else 0,
                                "source_url": url,
                                "navigation": navigation,
                                "scraping_metadata": chapter_data.get("metadata", {})
                            }
                        }
                        results.append(result)
                        self.log_info(f"✅ Chapter {i+1} scraped successfully: {len(content)} characters")
                    else:
                        failed_urls.append(chapter_url)
                        self.log_error(f"❌ Chapter {i+1} failed: No data returned")

                except Exception as e:
                    failed_urls.append(chapter_url)
                    self.log_error(f"❌ Chapter {i+1} failed: {e}")

            success_count = len(results)
            failed_count = len(failed_urls)

            self.log_info(f"Batch scraping completed: {success_count} success, {failed_count} failed")

            return results

        except Exception as e:
            self.log_error(f"Error in batch chapter scraping: {e}")
            raise ScrapingError(f"Batch scraping failed: {e}")
    
    async def test_connection(self) -> str:
        """Test scraping service connection and functionality"""
        try:
            await self._initialize()

            if self._initialized and self._scraper:
                # Test with a simple URL
                test_url = "https://webtruyen.diendantruyen.com/"
                test_result = await self._scraper.test_target_site(test_url, "webtruyen")

                if test_result.get("success"):
                    return "Real scraping service connected and ready"
                else:
                    return f"Scraping service connected but test failed: {test_result.get('errors', [])}"
            else:
                return "Scraping service initialization failed"

        except Exception as e:
            self.log_error(f"Connection test failed: {e}")
            return f"Connection test failed: {e}"

    async def get_scraping_stats(self) -> Dict[str, Any]:
        """Get scraping service statistics"""
        stats = {
            "initialized": self._initialized,
            "scraper_path": str(self._scraper_path),
            "max_concurrent": self.settings.max_concurrent_scraping,
            "rate_limit_delay": self.settings.scraping_delay_min,
            "timeout": self.settings.scraping_timeout
        }

        if self._scraper and self._initialized:
            try:
                scraper_stats = self._scraper.get_statistics()
                stats.update(scraper_stats)
            except Exception as e:
                self.log_error(f"Failed to get scraper statistics: {e}")
                stats["scraper_stats_error"] = str(e)

        return stats
    
    def __del__(self):
        """Cleanup on destruction"""
        if self._initialized:
            try:
                # Try to cleanup if event loop is available
                loop = asyncio.get_event_loop()
                if loop.is_running():
                    loop.create_task(self._cleanup())
                else:
                    loop.run_until_complete(self._cleanup())
            except:
                pass  # Ignore cleanup errors during destruction


# ============================================================================
# Service Factory
# ============================================================================

_scraping_service_instance: Optional[ScrapingService] = None


def get_scraping_service() -> ScrapingService:
    """Get singleton scraping service instance"""
    global _scraping_service_instance
    
    if _scraping_service_instance is None:
        _scraping_service_instance = ScrapingService()
    
    return _scraping_service_instance


async def cleanup_scraping_service():
    """Cleanup scraping service on application shutdown"""
    global _scraping_service_instance
    
    if _scraping_service_instance:
        await _scraping_service_instance._cleanup()
        _scraping_service_instance = None
