2025-07-09 08:40:13 | INFO     | utils.logging_config:setup_logging:93 | ✅ Logging configured - Level: INFO, File: /app/logs/api.log
2025-07-09 08:40:13 | INFO     | logging:callHandlers:1706 | 🚀 Starting Vietnamese Web Novel API...
2025-07-09 08:40:13 | INFO     | logging:callHandlers:1706 | ✅ Database connection established
2025-07-09 08:40:13 | INFO     | logging:callHandlers:1706 | ✅ Application startup completed
2025-07-09 08:40:16 | INFO     | logging:callHandlers:1706 | 🔵 GET /health - Client: 127.0.0.1 - User-Agent: curl/7.88.1...
2025-07-09 08:40:16 | INFO     | logging:callHandlers:1706 | ✅ GET /health - Status: 200 - Time: 0.001s - Size: 96
2025-07-09 08:40:18 | INFO     | logging:callHandlers:1706 | 🔵 GET /docs - Client: 172.18.0.1 - User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Sa...
2025-07-09 08:40:18 | INFO     | logging:callHandlers:1706 | ⚠️ GET /docs - Status: 404 - Time: 0.001s - Size: 74
2025-07-09 08:40:20 | INFO     | logging:callHandlers:1706 | 🔵 GET /docs - Client: 172.18.0.1 - User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Sa...
2025-07-09 08:40:20 | INFO     | logging:callHandlers:1706 | ⚠️ GET /docs - Status: 404 - Time: 0.001s - Size: 74
2025-07-09 08:40:26 | INFO     | logging:callHandlers:1706 | 🔵 GET /health - Client: 172.18.0.1 - User-Agent: curl/8.13.0...
2025-07-09 08:40:26 | INFO     | logging:callHandlers:1706 | ✅ GET /health - Status: 200 - Time: 0.001s - Size: 96
2025-07-09 08:40:31 | INFO     | logging:callHandlers:1706 | 🔵 GET /health/database - Client: 172.18.0.1 - User-Agent: curl/8.13.0...
2025-07-09 08:40:31 | INFO     | logging:callHandlers:1706 | ✅ GET /health/database - Status: 200 - Time: 0.003s - Size: 94
2025-07-09 08:40:36 | INFO     | logging:callHandlers:1706 | 🔵 GET /health - Client: 172.18.0.1 - User-Agent: curl/8.13.0...
2025-07-09 08:40:36 | INFO     | logging:callHandlers:1706 | ✅ GET /health - Status: 200 - Time: 0.001s - Size: 96
2025-07-09 08:40:40 | INFO     | logging:callHandlers:1706 | 🔵 GET / - Client: 172.18.0.1 - User-Agent: curl/8.13.0...
2025-07-09 08:40:40 | INFO     | logging:callHandlers:1706 | ✅ GET / - Status: 200 - Time: 0.001s - Size: 187
2025-07-09 08:40:46 | INFO     | logging:callHandlers:1706 | 🔵 GET /health - Client: 127.0.0.1 - User-Agent: curl/7.88.1...
2025-07-09 08:40:46 | INFO     | logging:callHandlers:1706 | ✅ GET /health - Status: 200 - Time: 0.001s - Size: 96
2025-07-09 08:41:17 | INFO     | logging:callHandlers:1706 | 🔵 GET /health - Client: 127.0.0.1 - User-Agent: curl/7.88.1...
2025-07-09 08:41:17 | INFO     | logging:callHandlers:1706 | ✅ GET /health - Status: 200 - Time: 0.001s - Size: 96
2025-07-09 08:41:47 | INFO     | logging:callHandlers:1706 | 🔵 GET /health - Client: 127.0.0.1 - User-Agent: curl/7.88.1...
2025-07-09 08:41:47 | INFO     | logging:callHandlers:1706 | ✅ GET /health - Status: 200 - Time: 0.001s - Size: 96
2025-07-09 08:42:17 | INFO     | logging:callHandlers:1706 | 🔵 GET /health - Client: 127.0.0.1 - User-Agent: curl/7.88.1...
2025-07-09 08:42:17 | INFO     | logging:callHandlers:1706 | ✅ GET /health - Status: 200 - Time: 0.001s - Size: 96
2025-07-09 08:42:47 | INFO     | logging:callHandlers:1706 | 🔵 GET /health - Client: 127.0.0.1 - User-Agent: curl/7.88.1...
2025-07-09 08:42:47 | INFO     | logging:callHandlers:1706 | ✅ GET /health - Status: 200 - Time: 0.001s - Size: 96
2025-07-09 08:43:17 | INFO     | logging:callHandlers:1706 | 🔵 GET /health - Client: 127.0.0.1 - User-Agent: curl/7.88.1...
2025-07-09 08:43:17 | INFO     | logging:callHandlers:1706 | ✅ GET /health - Status: 200 - Time: 0.001s - Size: 96
2025-07-09 08:43:47 | INFO     | logging:callHandlers:1706 | 🔵 GET /health - Client: 127.0.0.1 - User-Agent: curl/7.88.1...
2025-07-09 08:43:47 | INFO     | logging:callHandlers:1706 | ✅ GET /health - Status: 200 - Time: 0.001s - Size: 96
2025-07-09 08:44:17 | INFO     | logging:callHandlers:1706 | 🔵 GET /health - Client: 127.0.0.1 - User-Agent: curl/7.88.1...
2025-07-09 08:44:17 | INFO     | logging:callHandlers:1706 | ✅ GET /health - Status: 200 - Time: 0.001s - Size: 96
2025-07-09 08:44:38 | INFO     | logging:callHandlers:1706 | 🛑 Shutting down Vietnamese Web Novel API...
2025-07-09 08:44:38 | INFO     | logging:callHandlers:1706 | ✅ Application shutdown completed
2025-07-09 08:44:41 | INFO     | utils.logging_config:setup_logging:93 | ✅ Logging configured - Level: INFO, File: /app/logs/api.log
2025-07-09 08:44:41 | INFO     | logging:callHandlers:1706 | 🚀 Starting Vietnamese Web Novel API...
2025-07-09 08:44:41 | INFO     | logging:callHandlers:1706 | ✅ Database connection established
2025-07-09 08:44:41 | INFO     | logging:callHandlers:1706 | ✅ Application startup completed
2025-07-09 08:44:44 | INFO     | logging:callHandlers:1706 | 🔵 GET /health - Client: 127.0.0.1 - User-Agent: curl/7.88.1...
2025-07-09 08:44:44 | INFO     | logging:callHandlers:1706 | ✅ GET /health - Status: 200 - Time: 0.001s - Size: 96
2025-07-09 08:44:45 | INFO     | logging:callHandlers:1706 | 🔵 GET /docs - Client: 172.18.0.1 - User-Agent: curl/8.13.0...
2025-07-09 08:44:45 | INFO     | logging:callHandlers:1706 | ⚠️ GET /docs - Status: 404 - Time: 0.002s - Size: 74
2025-07-09 08:44:45 | INFO     | logging:callHandlers:1706 | 🔵 GET /docs - Client: 172.18.0.1 - User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Sa...
2025-07-09 08:44:45 | INFO     | logging:callHandlers:1706 | ⚠️ GET /docs - Status: 404 - Time: 0.001s - Size: 74
2025-07-09 08:44:47 | INFO     | logging:callHandlers:1706 | 🔵 GET /docs - Client: 172.18.0.1 - User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Sa...
2025-07-09 08:44:47 | INFO     | logging:callHandlers:1706 | ⚠️ GET /docs - Status: 404 - Time: 0.001s - Size: 74
2025-07-09 08:44:47 | INFO     | logging:callHandlers:1706 | 🔵 GET /docs - Client: 172.18.0.1 - User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Sa...
2025-07-09 08:44:47 | INFO     | logging:callHandlers:1706 | ⚠️ GET /docs - Status: 404 - Time: 0.001s - Size: 74
2025-07-09 08:44:48 | INFO     | logging:callHandlers:1706 | 🔵 GET /docs - Client: 172.18.0.1 - User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Sa...
2025-07-09 08:44:48 | INFO     | logging:callHandlers:1706 | ⚠️ GET /docs - Status: 404 - Time: 0.001s - Size: 74
2025-07-09 08:44:56 | INFO     | logging:callHandlers:1706 | 🛑 Shutting down Vietnamese Web Novel API...
2025-07-09 08:44:56 | INFO     | logging:callHandlers:1706 | ✅ Application shutdown completed
2025-07-09 08:44:58 | INFO     | utils.logging_config:setup_logging:93 | ✅ Logging configured - Level: INFO, File: /app/logs/api.log
2025-07-09 08:44:58 | INFO     | logging:callHandlers:1706 | 🚀 Starting Vietnamese Web Novel API...
2025-07-09 08:44:58 | INFO     | logging:callHandlers:1706 | ✅ Database connection established
2025-07-09 08:44:58 | INFO     | logging:callHandlers:1706 | ✅ Application startup completed
2025-07-09 08:45:02 | INFO     | logging:callHandlers:1706 | 🔵 GET /health - Client: 127.0.0.1 - User-Agent: curl/7.88.1...
2025-07-09 08:45:02 | INFO     | logging:callHandlers:1706 | ✅ GET /health - Status: 200 - Time: 0.001s - Size: 96
2025-07-09 08:45:04 | INFO     | logging:callHandlers:1706 | 🔵 HEAD /docs - Client: 172.18.0.1 - User-Agent: curl/8.13.0...
2025-07-09 08:45:04 | INFO     | logging:callHandlers:1706 | ✅ HEAD /docs - Status: 200 - Time: 0.001s - Size: 948
2025-07-09 08:45:08 | INFO     | logging:callHandlers:1706 | 🔵 GET /docs - Client: 172.18.0.1 - User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Sa...
2025-07-09 08:45:08 | INFO     | logging:callHandlers:1706 | ✅ GET /docs - Status: 200 - Time: 0.001s - Size: 948
2025-07-09 08:45:08 | INFO     | logging:callHandlers:1706 | 🔵 GET /openapi.json - Client: 172.18.0.1 - User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Sa...
2025-07-09 08:45:08 | INFO     | logging:callHandlers:1706 | ✅ GET /openapi.json - Status: 200 - Time: 0.022s - Size: 38154
2025-07-09 08:45:10 | INFO     | logging:callHandlers:1706 | 🔵 GET /docs - Client: 172.18.0.1 - User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Sa...
2025-07-09 08:45:10 | INFO     | logging:callHandlers:1706 | ✅ GET /docs - Status: 200 - Time: 0.001s - Size: 948
2025-07-09 08:45:10 | INFO     | logging:callHandlers:1706 | 🔵 GET /openapi.json - Client: 172.18.0.1 - User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Sa...
2025-07-09 08:45:10 | INFO     | logging:callHandlers:1706 | ✅ GET /openapi.json - Status: 200 - Time: 0.002s - Size: 38154
2025-07-09 08:45:14 | INFO     | logging:callHandlers:1706 | 🔵 GET /redoc - Client: 172.18.0.1 - User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Sa...
2025-07-09 08:45:14 | INFO     | logging:callHandlers:1706 | ✅ GET /redoc - Status: 200 - Time: 0.001s - Size: 905
2025-07-09 08:45:14 | INFO     | logging:callHandlers:1706 | 🔵 GET /openapi.json - Client: 172.18.0.1 - User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Sa...
2025-07-09 08:45:14 | INFO     | logging:callHandlers:1706 | ✅ GET /openapi.json - Status: 200 - Time: 0.001s - Size: 38154
2025-07-09 08:45:32 | INFO     | logging:callHandlers:1706 | 🔵 GET /health - Client: 127.0.0.1 - User-Agent: curl/7.88.1...
2025-07-09 08:45:32 | INFO     | logging:callHandlers:1706 | ✅ GET /health - Status: 200 - Time: 0.001s - Size: 96
2025-07-09 08:46:02 | INFO     | logging:callHandlers:1706 | 🔵 GET /health - Client: 127.0.0.1 - User-Agent: curl/7.88.1...
2025-07-09 08:46:02 | INFO     | logging:callHandlers:1706 | ✅ GET /health - Status: 200 - Time: 0.001s - Size: 96
