2025-07-09 08:46:37 | ERROR    | src.scraper_engine:start:125 | Failed to initialize scraper engine: BrowserType.launch: Executable doesn't exist at /home/<USER>/.cache/ms-playwright/chromium_headless_shell-1179/chrome-linux/headless_shell
╔════════════════════════════════════════════════════════════╗
║ Looks like <PERSON><PERSON> was just installed or updated.       ║
║ Please run the following command to download new browsers: ║
║                                                            ║
║     playwright install                                     ║
║                                                            ║
║ <3 Playwright Team                                         ║
╚════════════════════════════════════════════════════════════╝

2025-07-09 08:46:37 | ERROR    | logging:callHandlers:1706 | Failed to initialize scraping service: BrowserType.launch: Executable doesn't exist at /home/<USER>/.cache/ms-playwright/chromium_headless_shell-1179/chrome-linux/headless_shell
╔════════════════════════════════════════════════════════════╗
║ Looks like <PERSON><PERSON> was just installed or updated.       ║
║ Please run the following command to download new browsers: ║
║                                                            ║
║     playwright install                                     ║
║                                                            ║
║ <3 Playwright Team                                         ║
╚════════════════════════════════════════════════════════════╝
Traceback (most recent call last):

  File "/app/API/main.py", line 288, in <module>
    uvicorn.run(
    │       └ <function run at 0x71fba308dda0>
    └ <module 'uvicorn' from '/usr/local/lib/python3.11/site-packages/uvicorn/__init__.py'>

  File "/usr/local/lib/python3.11/site-packages/uvicorn/main.py", line 580, in run
    server.run()
    │      └ <function Server.run at 0x71fba308df80>
    └ <uvicorn.server.Server object at 0x71fba4f63d90>
  File "/usr/local/lib/python3.11/site-packages/uvicorn/server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x71fba308e020>
           │       │   └ <uvicorn.server.Server object at 0x71fba4f63d90>
           │       └ <function run at 0x71fba7acd620>
           └ <module 'asyncio' from '/usr/local/lib/python3.11/asyncio/__init__.py'>
  File "/usr/local/lib/python3.11/asyncio/runners.py", line 190, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x71fba30773d0>
           │      └ <function Runner.run at 0x71fba73d0400>
           └ <asyncio.runners.Runner object at 0x71fba32e9910>
  File "/usr/local/lib/python3.11/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at /usr/local/lib/python3.11/site-packages/uvicorn/server.py:71> wai...
           │    │     └ <cyfunction Loop.run_until_complete at 0x71fba305fe00>
           │    └ <uvloop.Loop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x71fba32e9910>
  File "/usr/local/lib/python3.11/site-packages/starlette/middleware/base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x71fba2925f80>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fba2925b20>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │    └ <starlette.middleware.trustedhost.TrustedHostMiddleware object at 0x71fba32e8e50>
          └ <middleware.error_handling.ErrorHandlingMiddleware object at 0x71fba34af090>
  File "/usr/local/lib/python3.11/site-packages/starlette/middleware/trustedhost.py", line 51, in __call__
    await self.app(scope, receive, send)
          │    │   │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x71fba2925f80>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fba2925b20>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │    └ <starlette.middleware.cors.CORSMiddleware object at 0x71fba30cf910>
          └ <starlette.middleware.trustedhost.TrustedHostMiddleware object at 0x71fba32e8e50>
  File "/usr/local/lib/python3.11/site-packages/starlette/middleware/cors.py", line 93, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
          │    │               │      │        │                     └ Headers({'host': 'localhost:8000', 'connection': 'keep-alive', 'content-length': '160', 'sec-ch-ua-platform': '"Windows"', 'u...
          │    │               │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x71fba2925f80>
          │    │               │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fba2925b20>
          │    │               └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │    └ <function CORSMiddleware.simple_response at 0x71fba6552de0>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x71fba30cf910>
  File "/usr/local/lib/python3.11/site-packages/starlette/middleware/cors.py", line 144, in simple_response
    await self.app(scope, receive, send)
          │    │   │      │        └ functools.partial(<bound method CORSMiddleware.send of <starlette.middleware.cors.CORSMiddleware object at 0x71fba30cf910>>, ...
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fba2925b20>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x71fba34a0990>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x71fba30cf910>
  File "/usr/local/lib/python3.11/site-packages/starlette/middleware/exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ functools.partial(<bound method CORSMiddleware.send of <starlette.middleware.cors.CORSMiddleware object at 0x71fba30cf910>>, ...
          │                            │    │    │     │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fba2925b20>
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │                            │    │    └ <starlette.requests.Request object at 0x71fba296e910>
          │                            │    └ <fastapi.routing.APIRouter object at 0x71fba2ad7950>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x71fba34a0990>
          └ <function wrap_app_handling_exceptions at 0x71fba64e1f80>
  File "/usr/local/lib/python3.11/site-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x71fba2925a80>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fba2925b20>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          └ <fastapi.routing.APIRouter object at 0x71fba2ad7950>
  File "/usr/local/lib/python3.11/site-packages/starlette/routing.py", line 714, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x71fba2925a80>
          │    │                │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fba2925b20>
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x71fba2ad7950>>
          └ <fastapi.routing.APIRouter object at 0x71fba2ad7950>
  File "/usr/local/lib/python3.11/site-packages/starlette/routing.py", line 734, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x71fba2925a80>
          │     │      │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fba2925b20>
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │     └ <function Route.handle at 0x71fba64e3560>
          └ APIRoute(path='/api/v1/scraping/story-info', name='scrape_story_info', methods=['POST'])
  File "/usr/local/lib/python3.11/site-packages/starlette/routing.py", line 288, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x71fba2925a80>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fba2925b20>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │    └ <function request_response.<locals>.app at 0x71fba2ae8180>
          └ APIRoute(path='/api/v1/scraping/story-info', name='scrape_story_info', methods=['POST'])
  File "/usr/local/lib/python3.11/site-packages/starlette/routing.py", line 76, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
          │                            │    │        │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x71fba2925a80>
          │                            │    │        │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fba2925b20>
          │                            │    │        └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │                            │    └ <starlette.requests.Request object at 0x71fba296c050>
          │                            └ <function request_response.<locals>.app.<locals>.app at 0x71fba2924220>
          └ <function wrap_app_handling_exceptions at 0x71fba64e1f80>
  File "/usr/local/lib/python3.11/site-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x71fba2925c60>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fba2925b20>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          └ <function request_response.<locals>.app.<locals>.app at 0x71fba2924220>
  File "/usr/local/lib/python3.11/site-packages/starlette/routing.py", line 73, in app
    response = await f(request)
                     │ └ <starlette.requests.Request object at 0x71fba296c050>
                     └ <function get_request_handler.<locals>.app at 0x71fba2ae80e0>
  File "/usr/local/lib/python3.11/site-packages/fastapi/routing.py", line 301, in app
    raw_response = await run_endpoint_function(
                         └ <function run_endpoint_function at 0x71fba7ecb600>
  File "/usr/local/lib/python3.11/site-packages/fastapi/routing.py", line 212, in run_endpoint_function
    return await dependant.call(**values)
                 │         │      └ {'request': StoryInfoRequest(story_url=HttpUrl('https://webtruyen.diendantruyen.com/truyen/vo-dao-truong-sinh-ta-tu-hanh-co-k...
                 │         └ <function scrape_story_info at 0x71fba317eb60>
                 └ Dependant(path_params=[], query_params=[], header_params=[], cookie_params=[], body_params=[ModelField(field_info=Body(Pydant...

  File "/app/API/routers/scraping.py", line 459, in scrape_story_info
    return await controller.scrape_story_info(request, background_tasks)
                 │          │                 │        └ <fastapi.background.BackgroundTasks object at 0x71fba2952fd0>
                 │          │                 └ StoryInfoRequest(story_url=HttpUrl('https://webtruyen.diendantruyen.com/truyen/vo-dao-truong-sinh-ta-tu-hanh-co-kinh-nghiem')...
                 │          └ <function ScrapingController.scrape_story_info at 0x71fba4f02480>
                 └ <routers.scraping.ScrapingController object at 0x71fba32e89d0>

  File "/app/API/routers/scraping.py", line 54, in scrape_story_info
    story_data = await self.scraping_service.scrape_story_info(
                       │    │                └ <function ScrapingService.scrape_story_info at 0x71fba4f027a0>
                       │    └ <services.scraping_service.ScrapingService object at 0x71fba32eac10>
                       └ <routers.scraping.ScrapingController object at 0x71fba32e89d0>

  File "/app/API/services/scraping_service.py", line 104, in scrape_story_info
    await self._initialize()
          │    └ <function ScrapingService._initialize at 0x71fba4f02660>
          └ <services.scraping_service.ScrapingService object at 0x71fba32eac10>

> File "/app/API/services/scraping_service.py", line 62, in _initialize
    await self._scraper.start()
          │    │        └ <function MetruyenScraper.start at 0x71fb92588cc0>
          │    └ <src.metruyenscraper.MetruyenScraper object at 0x71fba29384d0>
          └ <services.scraping_service.ScrapingService object at 0x71fba32eac10>

  File "/app/Scraper/src/metruyenscraper.py", line 63, in start
    await self.scraper_engine.start()
          │    │              └ <function ScraperEngine.start at 0x71fb92573880>
          │    └ <src.scraper_engine.ScraperEngine object at 0x71fba34ac890>
          └ <src.metruyenscraper.MetruyenScraper object at 0x71fba29384d0>

  File "/app/Scraper/src/scraper_engine.py", line 95, in start
    self.browser = await playwright.chromium.launch(
    │    │               │          └ <property object at 0x71fb925429d0>
    │    │               └ <playwright._impl._playwright.Playwright object at 0x71fb925c4050>
    │    └ None
    └ <src.scraper_engine.ScraperEngine object at 0x71fba34ac890>

  File "/usr/local/lib/python3.11/site-packages/playwright/async_api/_generated.py", line 14438, in launch
    await self._impl_obj.launch(
          │    │         └ <function BrowserType.launch at 0x71fb9251b4c0>
          │    └ <BrowserType name=chromium executable_path=/home/<USER>/.cache/ms-playwright/chromium-1179/chrome-linux/chrome>
          └ <BrowserType name=chromium executable_path=/home/<USER>/.cache/ms-playwright/chromium-1179/chrome-linux/chrome>
  File "/usr/local/lib/python3.11/site-packages/playwright/_impl/_browser_type.py", line 98, in launch
    await self._channel.send(
          │    │        └ <function Channel.send at 0x71fb927b4180>
          │    └ <playwright._impl._connection.Channel object at 0x71fb925a2d10>
          └ <BrowserType name=chromium executable_path=/home/<USER>/.cache/ms-playwright/chromium-1179/chrome-linux/chrome>
  File "/usr/local/lib/python3.11/site-packages/playwright/_impl/_connection.py", line 69, in send
    return await self._connection.wrap_api_call(
                 │    │           └ <function Connection.wrap_api_call at 0x71fb92624d60>
                 │    └ <playwright._impl._connection.Connection object at 0x71fb925a0450>
                 └ <playwright._impl._connection.Channel object at 0x71fb925a2d10>
  File "/usr/local/lib/python3.11/site-packages/playwright/_impl/_connection.py", line 558, in wrap_api_call
    raise rewrite_error(error, f"{parsed_st['apiName']}: {error}") from None
          └ <function rewrite_error at 0x71fb9275c180>

playwright._impl._errors.Error: BrowserType.launch: Executable doesn't exist at /home/<USER>/.cache/ms-playwright/chromium_headless_shell-1179/chrome-linux/headless_shell
╔════════════════════════════════════════════════════════════╗
║ Looks like Playwright was just installed or updated.       ║
║ Please run the following command to download new browsers: ║
║                                                            ║
║     playwright install                                     ║
║                                                            ║
║ <3 Playwright Team                                         ║
╚════════════════════════════════════════════════════════════╝

Traceback (most recent call last):

  File "/app/API/main.py", line 288, in <module>
    uvicorn.run(
    │       └ <function run at 0x71fba308dda0>
    └ <module 'uvicorn' from '/usr/local/lib/python3.11/site-packages/uvicorn/__init__.py'>

  File "/usr/local/lib/python3.11/site-packages/uvicorn/main.py", line 580, in run
    server.run()
    │      └ <function Server.run at 0x71fba308df80>
    └ <uvicorn.server.Server object at 0x71fba4f63d90>
  File "/usr/local/lib/python3.11/site-packages/uvicorn/server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x71fba308e020>
           │       │   └ <uvicorn.server.Server object at 0x71fba4f63d90>
           │       └ <function run at 0x71fba7acd620>
           └ <module 'asyncio' from '/usr/local/lib/python3.11/asyncio/__init__.py'>
  File "/usr/local/lib/python3.11/asyncio/runners.py", line 190, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x71fba30773d0>
           │      └ <function Runner.run at 0x71fba73d0400>
           └ <asyncio.runners.Runner object at 0x71fba32e9910>
  File "/usr/local/lib/python3.11/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at /usr/local/lib/python3.11/site-packages/uvicorn/server.py:71> wai...
           │    │     └ <cyfunction Loop.run_until_complete at 0x71fba305fe00>
           │    └ <uvloop.Loop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x71fba32e9910>
  File "/usr/local/lib/python3.11/site-packages/starlette/middleware/base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x71fba2925f80>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fba2925b20>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │    └ <starlette.middleware.trustedhost.TrustedHostMiddleware object at 0x71fba32e8e50>
          └ <middleware.error_handling.ErrorHandlingMiddleware object at 0x71fba34af090>
  File "/usr/local/lib/python3.11/site-packages/starlette/middleware/trustedhost.py", line 51, in __call__
    await self.app(scope, receive, send)
          │    │   │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x71fba2925f80>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fba2925b20>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │    └ <starlette.middleware.cors.CORSMiddleware object at 0x71fba30cf910>
          └ <starlette.middleware.trustedhost.TrustedHostMiddleware object at 0x71fba32e8e50>
  File "/usr/local/lib/python3.11/site-packages/starlette/middleware/cors.py", line 93, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
          │    │               │      │        │                     └ Headers({'host': 'localhost:8000', 'connection': 'keep-alive', 'content-length': '160', 'sec-ch-ua-platform': '"Windows"', 'u...
          │    │               │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x71fba2925f80>
          │    │               │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fba2925b20>
          │    │               └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │    └ <function CORSMiddleware.simple_response at 0x71fba6552de0>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x71fba30cf910>
  File "/usr/local/lib/python3.11/site-packages/starlette/middleware/cors.py", line 144, in simple_response
    await self.app(scope, receive, send)
          │    │   │      │        └ functools.partial(<bound method CORSMiddleware.send of <starlette.middleware.cors.CORSMiddleware object at 0x71fba30cf910>>, ...
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fba2925b20>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x71fba34a0990>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x71fba30cf910>
  File "/usr/local/lib/python3.11/site-packages/starlette/middleware/exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ functools.partial(<bound method CORSMiddleware.send of <starlette.middleware.cors.CORSMiddleware object at 0x71fba30cf910>>, ...
          │                            │    │    │     │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fba2925b20>
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │                            │    │    └ <starlette.requests.Request object at 0x71fba296e910>
          │                            │    └ <fastapi.routing.APIRouter object at 0x71fba2ad7950>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x71fba34a0990>
          └ <function wrap_app_handling_exceptions at 0x71fba64e1f80>
  File "/usr/local/lib/python3.11/site-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x71fba2925a80>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fba2925b20>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          └ <fastapi.routing.APIRouter object at 0x71fba2ad7950>
  File "/usr/local/lib/python3.11/site-packages/starlette/routing.py", line 714, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x71fba2925a80>
          │    │                │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fba2925b20>
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x71fba2ad7950>>
          └ <fastapi.routing.APIRouter object at 0x71fba2ad7950>
  File "/usr/local/lib/python3.11/site-packages/starlette/routing.py", line 734, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x71fba2925a80>
          │     │      │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fba2925b20>
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │     └ <function Route.handle at 0x71fba64e3560>
          └ APIRoute(path='/api/v1/scraping/story-info', name='scrape_story_info', methods=['POST'])
  File "/usr/local/lib/python3.11/site-packages/starlette/routing.py", line 288, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x71fba2925a80>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fba2925b20>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │    └ <function request_response.<locals>.app at 0x71fba2ae8180>
          └ APIRoute(path='/api/v1/scraping/story-info', name='scrape_story_info', methods=['POST'])
  File "/usr/local/lib/python3.11/site-packages/starlette/routing.py", line 76, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
          │                            │    │        │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x71fba2925a80>
          │                            │    │        │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fba2925b20>
          │                            │    │        └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │                            │    └ <starlette.requests.Request object at 0x71fba296c050>
          │                            └ <function request_response.<locals>.app.<locals>.app at 0x71fba2924220>
          └ <function wrap_app_handling_exceptions at 0x71fba64e1f80>
  File "/usr/local/lib/python3.11/site-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x71fba2925c60>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fba2925b20>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          └ <function request_response.<locals>.app.<locals>.app at 0x71fba2924220>
  File "/usr/local/lib/python3.11/site-packages/starlette/routing.py", line 73, in app
    response = await f(request)
                     │ └ <starlette.requests.Request object at 0x71fba296c050>
                     └ <function get_request_handler.<locals>.app at 0x71fba2ae80e0>
  File "/usr/local/lib/python3.11/site-packages/fastapi/routing.py", line 301, in app
    raw_response = await run_endpoint_function(
                         └ <function run_endpoint_function at 0x71fba7ecb600>
  File "/usr/local/lib/python3.11/site-packages/fastapi/routing.py", line 212, in run_endpoint_function
    return await dependant.call(**values)
                 │         │      └ {'request': StoryInfoRequest(story_url=HttpUrl('https://webtruyen.diendantruyen.com/truyen/vo-dao-truong-sinh-ta-tu-hanh-co-k...
                 │         └ <function scrape_story_info at 0x71fba317eb60>
                 └ Dependant(path_params=[], query_params=[], header_params=[], cookie_params=[], body_params=[ModelField(field_info=Body(Pydant...

  File "/app/API/routers/scraping.py", line 459, in scrape_story_info
    return await controller.scrape_story_info(request, background_tasks)
                 │          │                 │        └ <fastapi.background.BackgroundTasks object at 0x71fba2952fd0>
                 │          │                 └ StoryInfoRequest(story_url=HttpUrl('https://webtruyen.diendantruyen.com/truyen/vo-dao-truong-sinh-ta-tu-hanh-co-kinh-nghiem')...
                 │          └ <function ScrapingController.scrape_story_info at 0x71fba4f02480>
                 └ <routers.scraping.ScrapingController object at 0x71fba32e89d0>

  File "/app/API/routers/scraping.py", line 54, in scrape_story_info
    story_data = await self.scraping_service.scrape_story_info(
                       │    │                └ <function ScrapingService.scrape_story_info at 0x71fba4f027a0>
                       │    └ <services.scraping_service.ScrapingService object at 0x71fba32eac10>
                       └ <routers.scraping.ScrapingController object at 0x71fba32e89d0>

  File "/app/API/services/scraping_service.py", line 104, in scrape_story_info
    await self._initialize()
          │    └ <function ScrapingService._initialize at 0x71fba4f02660>
          └ <services.scraping_service.ScrapingService object at 0x71fba32eac10>

> File "/app/API/services/scraping_service.py", line 62, in _initialize
    await self._scraper.start()
          │    │        └ <function MetruyenScraper.start at 0x71fb92588cc0>
          │    └ <src.metruyenscraper.MetruyenScraper object at 0x71fba29384d0>
          └ <services.scraping_service.ScrapingService object at 0x71fba32eac10>

  File "/app/Scraper/src/metruyenscraper.py", line 63, in start
    await self.scraper_engine.start()
          │    │              └ <function ScraperEngine.start at 0x71fb92573880>
          │    └ <src.scraper_engine.ScraperEngine object at 0x71fba34ac890>
          └ <src.metruyenscraper.MetruyenScraper object at 0x71fba29384d0>

  File "/app/Scraper/src/scraper_engine.py", line 95, in start
    self.browser = await playwright.chromium.launch(
    │    │               │          └ <property object at 0x71fb925429d0>
    │    │               └ <playwright._impl._playwright.Playwright object at 0x71fb925c4050>
    │    └ None
    └ <src.scraper_engine.ScraperEngine object at 0x71fba34ac890>

  File "/usr/local/lib/python3.11/site-packages/playwright/async_api/_generated.py", line 14438, in launch
    await self._impl_obj.launch(
          │    │         └ <function BrowserType.launch at 0x71fb9251b4c0>
          │    └ <BrowserType name=chromium executable_path=/home/<USER>/.cache/ms-playwright/chromium-1179/chrome-linux/chrome>
          └ <BrowserType name=chromium executable_path=/home/<USER>/.cache/ms-playwright/chromium-1179/chrome-linux/chrome>
  File "/usr/local/lib/python3.11/site-packages/playwright/_impl/_browser_type.py", line 98, in launch
    await self._channel.send(
          │    │        └ <function Channel.send at 0x71fb927b4180>
          │    └ <playwright._impl._connection.Channel object at 0x71fb925a2d10>
          └ <BrowserType name=chromium executable_path=/home/<USER>/.cache/ms-playwright/chromium-1179/chrome-linux/chrome>
  File "/usr/local/lib/python3.11/site-packages/playwright/_impl/_connection.py", line 69, in send
    return await self._connection.wrap_api_call(
                 │    │           └ <function Connection.wrap_api_call at 0x71fb92624d60>
                 │    └ <playwright._impl._connection.Connection object at 0x71fb925a0450>
                 └ <playwright._impl._connection.Channel object at 0x71fb925a2d10>
  File "/usr/local/lib/python3.11/site-packages/playwright/_impl/_connection.py", line 558, in wrap_api_call
    raise rewrite_error(error, f"{parsed_st['apiName']}: {error}") from None
          └ <function rewrite_error at 0x71fb9275c180>

playwright._impl._errors.Error: BrowserType.launch: Executable doesn't exist at /home/<USER>/.cache/ms-playwright/chromium_headless_shell-1179/chrome-linux/headless_shell
╔════════════════════════════════════════════════════════════╗
║ Looks like Playwright was just installed or updated.       ║
║ Please run the following command to download new browsers: ║
║                                                            ║
║     playwright install                                     ║
║                                                            ║
║ <3 Playwright Team                                         ║
╚════════════════════════════════════════════════════════════╝
2025-07-09 08:46:37 | ERROR    | logging:callHandlers:1706 | Exception type: Error
Traceback (most recent call last):

  File "/app/API/main.py", line 288, in <module>
    uvicorn.run(
    │       └ <function run at 0x71fba308dda0>
    └ <module 'uvicorn' from '/usr/local/lib/python3.11/site-packages/uvicorn/__init__.py'>

  File "/usr/local/lib/python3.11/site-packages/uvicorn/main.py", line 580, in run
    server.run()
    │      └ <function Server.run at 0x71fba308df80>
    └ <uvicorn.server.Server object at 0x71fba4f63d90>
  File "/usr/local/lib/python3.11/site-packages/uvicorn/server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x71fba308e020>
           │       │   └ <uvicorn.server.Server object at 0x71fba4f63d90>
           │       └ <function run at 0x71fba7acd620>
           └ <module 'asyncio' from '/usr/local/lib/python3.11/asyncio/__init__.py'>
  File "/usr/local/lib/python3.11/asyncio/runners.py", line 190, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x71fba30773d0>
           │      └ <function Runner.run at 0x71fba73d0400>
           └ <asyncio.runners.Runner object at 0x71fba32e9910>
  File "/usr/local/lib/python3.11/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at /usr/local/lib/python3.11/site-packages/uvicorn/server.py:71> wai...
           │    │     └ <cyfunction Loop.run_until_complete at 0x71fba305fe00>
           │    └ <uvloop.Loop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x71fba32e9910>
  File "/usr/local/lib/python3.11/site-packages/starlette/middleware/base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x71fba2925f80>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fba2925b20>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │    └ <starlette.middleware.trustedhost.TrustedHostMiddleware object at 0x71fba32e8e50>
          └ <middleware.error_handling.ErrorHandlingMiddleware object at 0x71fba34af090>
  File "/usr/local/lib/python3.11/site-packages/starlette/middleware/trustedhost.py", line 51, in __call__
    await self.app(scope, receive, send)
          │    │   │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x71fba2925f80>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fba2925b20>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │    └ <starlette.middleware.cors.CORSMiddleware object at 0x71fba30cf910>
          └ <starlette.middleware.trustedhost.TrustedHostMiddleware object at 0x71fba32e8e50>
  File "/usr/local/lib/python3.11/site-packages/starlette/middleware/cors.py", line 93, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
          │    │               │      │        │                     └ Headers({'host': 'localhost:8000', 'connection': 'keep-alive', 'content-length': '160', 'sec-ch-ua-platform': '"Windows"', 'u...
          │    │               │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x71fba2925f80>
          │    │               │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fba2925b20>
          │    │               └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │    └ <function CORSMiddleware.simple_response at 0x71fba6552de0>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x71fba30cf910>
  File "/usr/local/lib/python3.11/site-packages/starlette/middleware/cors.py", line 144, in simple_response
    await self.app(scope, receive, send)
          │    │   │      │        └ functools.partial(<bound method CORSMiddleware.send of <starlette.middleware.cors.CORSMiddleware object at 0x71fba30cf910>>, ...
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fba2925b20>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x71fba34a0990>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x71fba30cf910>
  File "/usr/local/lib/python3.11/site-packages/starlette/middleware/exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ functools.partial(<bound method CORSMiddleware.send of <starlette.middleware.cors.CORSMiddleware object at 0x71fba30cf910>>, ...
          │                            │    │    │     │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fba2925b20>
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │                            │    │    └ <starlette.requests.Request object at 0x71fba296e910>
          │                            │    └ <fastapi.routing.APIRouter object at 0x71fba2ad7950>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x71fba34a0990>
          └ <function wrap_app_handling_exceptions at 0x71fba64e1f80>
  File "/usr/local/lib/python3.11/site-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x71fba2925a80>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fba2925b20>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          └ <fastapi.routing.APIRouter object at 0x71fba2ad7950>
  File "/usr/local/lib/python3.11/site-packages/starlette/routing.py", line 714, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x71fba2925a80>
          │    │                │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fba2925b20>
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x71fba2ad7950>>
          └ <fastapi.routing.APIRouter object at 0x71fba2ad7950>
  File "/usr/local/lib/python3.11/site-packages/starlette/routing.py", line 734, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x71fba2925a80>
          │     │      │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fba2925b20>
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │     └ <function Route.handle at 0x71fba64e3560>
          └ APIRoute(path='/api/v1/scraping/story-info', name='scrape_story_info', methods=['POST'])
  File "/usr/local/lib/python3.11/site-packages/starlette/routing.py", line 288, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x71fba2925a80>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fba2925b20>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │    └ <function request_response.<locals>.app at 0x71fba2ae8180>
          └ APIRoute(path='/api/v1/scraping/story-info', name='scrape_story_info', methods=['POST'])
  File "/usr/local/lib/python3.11/site-packages/starlette/routing.py", line 76, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
          │                            │    │        │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x71fba2925a80>
          │                            │    │        │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fba2925b20>
          │                            │    │        └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │                            │    └ <starlette.requests.Request object at 0x71fba296c050>
          │                            └ <function request_response.<locals>.app.<locals>.app at 0x71fba2924220>
          └ <function wrap_app_handling_exceptions at 0x71fba64e1f80>
  File "/usr/local/lib/python3.11/site-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x71fba2925c60>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fba2925b20>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          └ <function request_response.<locals>.app.<locals>.app at 0x71fba2924220>
  File "/usr/local/lib/python3.11/site-packages/starlette/routing.py", line 73, in app
    response = await f(request)
                     │ └ <starlette.requests.Request object at 0x71fba296c050>
                     └ <function get_request_handler.<locals>.app at 0x71fba2ae80e0>
  File "/usr/local/lib/python3.11/site-packages/fastapi/routing.py", line 301, in app
    raw_response = await run_endpoint_function(
                         └ <function run_endpoint_function at 0x71fba7ecb600>
  File "/usr/local/lib/python3.11/site-packages/fastapi/routing.py", line 212, in run_endpoint_function
    return await dependant.call(**values)
                 │         │      └ {'request': StoryInfoRequest(story_url=HttpUrl('https://webtruyen.diendantruyen.com/truyen/vo-dao-truong-sinh-ta-tu-hanh-co-k...
                 │         └ <function scrape_story_info at 0x71fba317eb60>
                 └ Dependant(path_params=[], query_params=[], header_params=[], cookie_params=[], body_params=[ModelField(field_info=Body(Pydant...

  File "/app/API/routers/scraping.py", line 459, in scrape_story_info
    return await controller.scrape_story_info(request, background_tasks)
                 │          │                 │        └ <fastapi.background.BackgroundTasks object at 0x71fba2952fd0>
                 │          │                 └ StoryInfoRequest(story_url=HttpUrl('https://webtruyen.diendantruyen.com/truyen/vo-dao-truong-sinh-ta-tu-hanh-co-kinh-nghiem')...
                 │          └ <function ScrapingController.scrape_story_info at 0x71fba4f02480>
                 └ <routers.scraping.ScrapingController object at 0x71fba32e89d0>

  File "/app/API/routers/scraping.py", line 54, in scrape_story_info
    story_data = await self.scraping_service.scrape_story_info(
                       │    │                └ <function ScrapingService.scrape_story_info at 0x71fba4f027a0>
                       │    └ <services.scraping_service.ScrapingService object at 0x71fba32eac10>
                       └ <routers.scraping.ScrapingController object at 0x71fba32e89d0>

  File "/app/API/services/scraping_service.py", line 104, in scrape_story_info
    await self._initialize()
          │    └ <function ScrapingService._initialize at 0x71fba4f02660>
          └ <services.scraping_service.ScrapingService object at 0x71fba32eac10>

> File "/app/API/services/scraping_service.py", line 62, in _initialize
    await self._scraper.start()
          │    │        └ <function MetruyenScraper.start at 0x71fb92588cc0>
          │    └ <src.metruyenscraper.MetruyenScraper object at 0x71fba29384d0>
          └ <services.scraping_service.ScrapingService object at 0x71fba32eac10>

  File "/app/Scraper/src/metruyenscraper.py", line 63, in start
    await self.scraper_engine.start()
          │    │              └ <function ScraperEngine.start at 0x71fb92573880>
          │    └ <src.scraper_engine.ScraperEngine object at 0x71fba34ac890>
          └ <src.metruyenscraper.MetruyenScraper object at 0x71fba29384d0>

  File "/app/Scraper/src/scraper_engine.py", line 95, in start
    self.browser = await playwright.chromium.launch(
    │    │               │          └ <property object at 0x71fb925429d0>
    │    │               └ <playwright._impl._playwright.Playwright object at 0x71fb925c4050>
    │    └ None
    └ <src.scraper_engine.ScraperEngine object at 0x71fba34ac890>

  File "/usr/local/lib/python3.11/site-packages/playwright/async_api/_generated.py", line 14438, in launch
    await self._impl_obj.launch(
          │    │         └ <function BrowserType.launch at 0x71fb9251b4c0>
          │    └ <BrowserType name=chromium executable_path=/home/<USER>/.cache/ms-playwright/chromium-1179/chrome-linux/chrome>
          └ <BrowserType name=chromium executable_path=/home/<USER>/.cache/ms-playwright/chromium-1179/chrome-linux/chrome>
  File "/usr/local/lib/python3.11/site-packages/playwright/_impl/_browser_type.py", line 98, in launch
    await self._channel.send(
          │    │        └ <function Channel.send at 0x71fb927b4180>
          │    └ <playwright._impl._connection.Channel object at 0x71fb925a2d10>
          └ <BrowserType name=chromium executable_path=/home/<USER>/.cache/ms-playwright/chromium-1179/chrome-linux/chrome>
  File "/usr/local/lib/python3.11/site-packages/playwright/_impl/_connection.py", line 69, in send
    return await self._connection.wrap_api_call(
                 │    │           └ <function Connection.wrap_api_call at 0x71fb92624d60>
                 │    └ <playwright._impl._connection.Connection object at 0x71fb925a0450>
                 └ <playwright._impl._connection.Channel object at 0x71fb925a2d10>
  File "/usr/local/lib/python3.11/site-packages/playwright/_impl/_connection.py", line 558, in wrap_api_call
    raise rewrite_error(error, f"{parsed_st['apiName']}: {error}") from None
          └ <function rewrite_error at 0x71fb9275c180>

playwright._impl._errors.Error: BrowserType.launch: Executable doesn't exist at /home/<USER>/.cache/ms-playwright/chromium_headless_shell-1179/chrome-linux/headless_shell
╔════════════════════════════════════════════════════════════╗
║ Looks like Playwright was just installed or updated.       ║
║ Please run the following command to download new browsers: ║
║                                                            ║
║     playwright install                                     ║
║                                                            ║
║ <3 Playwright Team                                         ║
╚════════════════════════════════════════════════════════════╝

Traceback (most recent call last):

  File "/app/API/main.py", line 288, in <module>
    uvicorn.run(
    │       └ <function run at 0x71fba308dda0>
    └ <module 'uvicorn' from '/usr/local/lib/python3.11/site-packages/uvicorn/__init__.py'>

  File "/usr/local/lib/python3.11/site-packages/uvicorn/main.py", line 580, in run
    server.run()
    │      └ <function Server.run at 0x71fba308df80>
    └ <uvicorn.server.Server object at 0x71fba4f63d90>
  File "/usr/local/lib/python3.11/site-packages/uvicorn/server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x71fba308e020>
           │       │   └ <uvicorn.server.Server object at 0x71fba4f63d90>
           │       └ <function run at 0x71fba7acd620>
           └ <module 'asyncio' from '/usr/local/lib/python3.11/asyncio/__init__.py'>
  File "/usr/local/lib/python3.11/asyncio/runners.py", line 190, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x71fba30773d0>
           │      └ <function Runner.run at 0x71fba73d0400>
           └ <asyncio.runners.Runner object at 0x71fba32e9910>
  File "/usr/local/lib/python3.11/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at /usr/local/lib/python3.11/site-packages/uvicorn/server.py:71> wai...
           │    │     └ <cyfunction Loop.run_until_complete at 0x71fba305fe00>
           │    └ <uvloop.Loop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x71fba32e9910>
  File "/usr/local/lib/python3.11/site-packages/starlette/middleware/base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x71fba2925f80>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fba2925b20>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │    └ <starlette.middleware.trustedhost.TrustedHostMiddleware object at 0x71fba32e8e50>
          └ <middleware.error_handling.ErrorHandlingMiddleware object at 0x71fba34af090>
  File "/usr/local/lib/python3.11/site-packages/starlette/middleware/trustedhost.py", line 51, in __call__
    await self.app(scope, receive, send)
          │    │   │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x71fba2925f80>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fba2925b20>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │    └ <starlette.middleware.cors.CORSMiddleware object at 0x71fba30cf910>
          └ <starlette.middleware.trustedhost.TrustedHostMiddleware object at 0x71fba32e8e50>
  File "/usr/local/lib/python3.11/site-packages/starlette/middleware/cors.py", line 93, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
          │    │               │      │        │                     └ Headers({'host': 'localhost:8000', 'connection': 'keep-alive', 'content-length': '160', 'sec-ch-ua-platform': '"Windows"', 'u...
          │    │               │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x71fba2925f80>
          │    │               │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fba2925b20>
          │    │               └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │    └ <function CORSMiddleware.simple_response at 0x71fba6552de0>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x71fba30cf910>
  File "/usr/local/lib/python3.11/site-packages/starlette/middleware/cors.py", line 144, in simple_response
    await self.app(scope, receive, send)
          │    │   │      │        └ functools.partial(<bound method CORSMiddleware.send of <starlette.middleware.cors.CORSMiddleware object at 0x71fba30cf910>>, ...
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fba2925b20>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x71fba34a0990>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x71fba30cf910>
  File "/usr/local/lib/python3.11/site-packages/starlette/middleware/exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ functools.partial(<bound method CORSMiddleware.send of <starlette.middleware.cors.CORSMiddleware object at 0x71fba30cf910>>, ...
          │                            │    │    │     │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fba2925b20>
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │                            │    │    └ <starlette.requests.Request object at 0x71fba296e910>
          │                            │    └ <fastapi.routing.APIRouter object at 0x71fba2ad7950>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x71fba34a0990>
          └ <function wrap_app_handling_exceptions at 0x71fba64e1f80>
  File "/usr/local/lib/python3.11/site-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x71fba2925a80>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fba2925b20>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          └ <fastapi.routing.APIRouter object at 0x71fba2ad7950>
  File "/usr/local/lib/python3.11/site-packages/starlette/routing.py", line 714, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x71fba2925a80>
          │    │                │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fba2925b20>
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x71fba2ad7950>>
          └ <fastapi.routing.APIRouter object at 0x71fba2ad7950>
  File "/usr/local/lib/python3.11/site-packages/starlette/routing.py", line 734, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x71fba2925a80>
          │     │      │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fba2925b20>
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │     └ <function Route.handle at 0x71fba64e3560>
          └ APIRoute(path='/api/v1/scraping/story-info', name='scrape_story_info', methods=['POST'])
  File "/usr/local/lib/python3.11/site-packages/starlette/routing.py", line 288, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x71fba2925a80>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fba2925b20>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │    └ <function request_response.<locals>.app at 0x71fba2ae8180>
          └ APIRoute(path='/api/v1/scraping/story-info', name='scrape_story_info', methods=['POST'])
  File "/usr/local/lib/python3.11/site-packages/starlette/routing.py", line 76, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
          │                            │    │        │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x71fba2925a80>
          │                            │    │        │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fba2925b20>
          │                            │    │        └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │                            │    └ <starlette.requests.Request object at 0x71fba296c050>
          │                            └ <function request_response.<locals>.app.<locals>.app at 0x71fba2924220>
          └ <function wrap_app_handling_exceptions at 0x71fba64e1f80>
  File "/usr/local/lib/python3.11/site-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x71fba2925c60>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fba2925b20>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          └ <function request_response.<locals>.app.<locals>.app at 0x71fba2924220>
  File "/usr/local/lib/python3.11/site-packages/starlette/routing.py", line 73, in app
    response = await f(request)
                     │ └ <starlette.requests.Request object at 0x71fba296c050>
                     └ <function get_request_handler.<locals>.app at 0x71fba2ae80e0>
  File "/usr/local/lib/python3.11/site-packages/fastapi/routing.py", line 301, in app
    raw_response = await run_endpoint_function(
                         └ <function run_endpoint_function at 0x71fba7ecb600>
  File "/usr/local/lib/python3.11/site-packages/fastapi/routing.py", line 212, in run_endpoint_function
    return await dependant.call(**values)
                 │         │      └ {'request': StoryInfoRequest(story_url=HttpUrl('https://webtruyen.diendantruyen.com/truyen/vo-dao-truong-sinh-ta-tu-hanh-co-k...
                 │         └ <function scrape_story_info at 0x71fba317eb60>
                 └ Dependant(path_params=[], query_params=[], header_params=[], cookie_params=[], body_params=[ModelField(field_info=Body(Pydant...

  File "/app/API/routers/scraping.py", line 459, in scrape_story_info
    return await controller.scrape_story_info(request, background_tasks)
                 │          │                 │        └ <fastapi.background.BackgroundTasks object at 0x71fba2952fd0>
                 │          │                 └ StoryInfoRequest(story_url=HttpUrl('https://webtruyen.diendantruyen.com/truyen/vo-dao-truong-sinh-ta-tu-hanh-co-kinh-nghiem')...
                 │          └ <function ScrapingController.scrape_story_info at 0x71fba4f02480>
                 └ <routers.scraping.ScrapingController object at 0x71fba32e89d0>

  File "/app/API/routers/scraping.py", line 54, in scrape_story_info
    story_data = await self.scraping_service.scrape_story_info(
                       │    │                └ <function ScrapingService.scrape_story_info at 0x71fba4f027a0>
                       │    └ <services.scraping_service.ScrapingService object at 0x71fba32eac10>
                       └ <routers.scraping.ScrapingController object at 0x71fba32e89d0>

  File "/app/API/services/scraping_service.py", line 104, in scrape_story_info
    await self._initialize()
          │    └ <function ScrapingService._initialize at 0x71fba4f02660>
          └ <services.scraping_service.ScrapingService object at 0x71fba32eac10>

> File "/app/API/services/scraping_service.py", line 62, in _initialize
    await self._scraper.start()
          │    │        └ <function MetruyenScraper.start at 0x71fb92588cc0>
          │    └ <src.metruyenscraper.MetruyenScraper object at 0x71fba29384d0>
          └ <services.scraping_service.ScrapingService object at 0x71fba32eac10>

  File "/app/Scraper/src/metruyenscraper.py", line 63, in start
    await self.scraper_engine.start()
          │    │              └ <function ScraperEngine.start at 0x71fb92573880>
          │    └ <src.scraper_engine.ScraperEngine object at 0x71fba34ac890>
          └ <src.metruyenscraper.MetruyenScraper object at 0x71fba29384d0>

  File "/app/Scraper/src/scraper_engine.py", line 95, in start
    self.browser = await playwright.chromium.launch(
    │    │               │          └ <property object at 0x71fb925429d0>
    │    │               └ <playwright._impl._playwright.Playwright object at 0x71fb925c4050>
    │    └ None
    └ <src.scraper_engine.ScraperEngine object at 0x71fba34ac890>

  File "/usr/local/lib/python3.11/site-packages/playwright/async_api/_generated.py", line 14438, in launch
    await self._impl_obj.launch(
          │    │         └ <function BrowserType.launch at 0x71fb9251b4c0>
          │    └ <BrowserType name=chromium executable_path=/home/<USER>/.cache/ms-playwright/chromium-1179/chrome-linux/chrome>
          └ <BrowserType name=chromium executable_path=/home/<USER>/.cache/ms-playwright/chromium-1179/chrome-linux/chrome>
  File "/usr/local/lib/python3.11/site-packages/playwright/_impl/_browser_type.py", line 98, in launch
    await self._channel.send(
          │    │        └ <function Channel.send at 0x71fb927b4180>
          │    └ <playwright._impl._connection.Channel object at 0x71fb925a2d10>
          └ <BrowserType name=chromium executable_path=/home/<USER>/.cache/ms-playwright/chromium-1179/chrome-linux/chrome>
  File "/usr/local/lib/python3.11/site-packages/playwright/_impl/_connection.py", line 69, in send
    return await self._connection.wrap_api_call(
                 │    │           └ <function Connection.wrap_api_call at 0x71fb92624d60>
                 │    └ <playwright._impl._connection.Connection object at 0x71fb925a0450>
                 └ <playwright._impl._connection.Channel object at 0x71fb925a2d10>
  File "/usr/local/lib/python3.11/site-packages/playwright/_impl/_connection.py", line 558, in wrap_api_call
    raise rewrite_error(error, f"{parsed_st['apiName']}: {error}") from None
          └ <function rewrite_error at 0x71fb9275c180>

playwright._impl._errors.Error: BrowserType.launch: Executable doesn't exist at /home/<USER>/.cache/ms-playwright/chromium_headless_shell-1179/chrome-linux/headless_shell
╔════════════════════════════════════════════════════════════╗
║ Looks like Playwright was just installed or updated.       ║
║ Please run the following command to download new browsers: ║
║                                                            ║
║     playwright install                                     ║
║                                                            ║
║ <3 Playwright Team                                         ║
╚════════════════════════════════════════════════════════════╝
2025-07-09 08:46:37 | ERROR    | logging:callHandlers:1706 | Traceback: Traceback (most recent call last):
  File "/app/API/services/scraping_service.py", line 62, in _initialize
    await self._scraper.start()
  File "/app/Scraper/src/metruyenscraper.py", line 63, in start
    await self.scraper_engine.start()
  File "/app/Scraper/src/scraper_engine.py", line 95, in start
    self.browser = await playwright.chromium.launch(
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/playwright/async_api/_generated.py", line 14438, in launch
    await self._impl_obj.launch(
  File "/usr/local/lib/python3.11/site-packages/playwright/_impl/_browser_type.py", line 98, in launch
    await self._channel.send(
  File "/usr/local/lib/python3.11/site-packages/playwright/_impl/_connection.py", line 69, in send
    return await self._connection.wrap_api_call(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/playwright/_impl/_connection.py", line 558, in wrap_api_call
    raise rewrite_error(error, f"{parsed_st['apiName']}: {error}") from None
playwright._impl._errors.Error: BrowserType.launch: Executable doesn't exist at /home/<USER>/.cache/ms-playwright/chromium_headless_shell-1179/chrome-linux/headless_shell
╔════════════════════════════════════════════════════════════╗
║ Looks like Playwright was just installed or updated.       ║
║ Please run the following command to download new browsers: ║
║                                                            ║
║     playwright install                                     ║
║                                                            ║
║ <3 Playwright Team                                         ║
╚════════════════════════════════════════════════════════════╝

Traceback (most recent call last):

  File "/app/API/main.py", line 288, in <module>
    uvicorn.run(
    │       └ <function run at 0x71fba308dda0>
    └ <module 'uvicorn' from '/usr/local/lib/python3.11/site-packages/uvicorn/__init__.py'>

  File "/usr/local/lib/python3.11/site-packages/uvicorn/main.py", line 580, in run
    server.run()
    │      └ <function Server.run at 0x71fba308df80>
    └ <uvicorn.server.Server object at 0x71fba4f63d90>
  File "/usr/local/lib/python3.11/site-packages/uvicorn/server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x71fba308e020>
           │       │   └ <uvicorn.server.Server object at 0x71fba4f63d90>
           │       └ <function run at 0x71fba7acd620>
           └ <module 'asyncio' from '/usr/local/lib/python3.11/asyncio/__init__.py'>
  File "/usr/local/lib/python3.11/asyncio/runners.py", line 190, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x71fba30773d0>
           │      └ <function Runner.run at 0x71fba73d0400>
           └ <asyncio.runners.Runner object at 0x71fba32e9910>
  File "/usr/local/lib/python3.11/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at /usr/local/lib/python3.11/site-packages/uvicorn/server.py:71> wai...
           │    │     └ <cyfunction Loop.run_until_complete at 0x71fba305fe00>
           │    └ <uvloop.Loop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x71fba32e9910>
  File "/usr/local/lib/python3.11/site-packages/starlette/middleware/base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x71fba2925f80>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fba2925b20>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │    └ <starlette.middleware.trustedhost.TrustedHostMiddleware object at 0x71fba32e8e50>
          └ <middleware.error_handling.ErrorHandlingMiddleware object at 0x71fba34af090>
  File "/usr/local/lib/python3.11/site-packages/starlette/middleware/trustedhost.py", line 51, in __call__
    await self.app(scope, receive, send)
          │    │   │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x71fba2925f80>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fba2925b20>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │    └ <starlette.middleware.cors.CORSMiddleware object at 0x71fba30cf910>
          └ <starlette.middleware.trustedhost.TrustedHostMiddleware object at 0x71fba32e8e50>
  File "/usr/local/lib/python3.11/site-packages/starlette/middleware/cors.py", line 93, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
          │    │               │      │        │                     └ Headers({'host': 'localhost:8000', 'connection': 'keep-alive', 'content-length': '160', 'sec-ch-ua-platform': '"Windows"', 'u...
          │    │               │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x71fba2925f80>
          │    │               │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fba2925b20>
          │    │               └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │    └ <function CORSMiddleware.simple_response at 0x71fba6552de0>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x71fba30cf910>
  File "/usr/local/lib/python3.11/site-packages/starlette/middleware/cors.py", line 144, in simple_response
    await self.app(scope, receive, send)
          │    │   │      │        └ functools.partial(<bound method CORSMiddleware.send of <starlette.middleware.cors.CORSMiddleware object at 0x71fba30cf910>>, ...
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fba2925b20>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x71fba34a0990>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x71fba30cf910>
  File "/usr/local/lib/python3.11/site-packages/starlette/middleware/exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ functools.partial(<bound method CORSMiddleware.send of <starlette.middleware.cors.CORSMiddleware object at 0x71fba30cf910>>, ...
          │                            │    │    │     │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fba2925b20>
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │                            │    │    └ <starlette.requests.Request object at 0x71fba296e910>
          │                            │    └ <fastapi.routing.APIRouter object at 0x71fba2ad7950>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x71fba34a0990>
          └ <function wrap_app_handling_exceptions at 0x71fba64e1f80>
  File "/usr/local/lib/python3.11/site-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x71fba2925a80>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fba2925b20>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          └ <fastapi.routing.APIRouter object at 0x71fba2ad7950>
  File "/usr/local/lib/python3.11/site-packages/starlette/routing.py", line 714, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x71fba2925a80>
          │    │                │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fba2925b20>
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x71fba2ad7950>>
          └ <fastapi.routing.APIRouter object at 0x71fba2ad7950>
  File "/usr/local/lib/python3.11/site-packages/starlette/routing.py", line 734, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x71fba2925a80>
          │     │      │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fba2925b20>
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │     └ <function Route.handle at 0x71fba64e3560>
          └ APIRoute(path='/api/v1/scraping/story-info', name='scrape_story_info', methods=['POST'])
  File "/usr/local/lib/python3.11/site-packages/starlette/routing.py", line 288, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x71fba2925a80>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fba2925b20>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │    └ <function request_response.<locals>.app at 0x71fba2ae8180>
          └ APIRoute(path='/api/v1/scraping/story-info', name='scrape_story_info', methods=['POST'])
  File "/usr/local/lib/python3.11/site-packages/starlette/routing.py", line 76, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
          │                            │    │        │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x71fba2925a80>
          │                            │    │        │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fba2925b20>
          │                            │    │        └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │                            │    └ <starlette.requests.Request object at 0x71fba296c050>
          │                            └ <function request_response.<locals>.app.<locals>.app at 0x71fba2924220>
          └ <function wrap_app_handling_exceptions at 0x71fba64e1f80>
  File "/usr/local/lib/python3.11/site-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x71fba2925c60>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fba2925b20>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          └ <function request_response.<locals>.app.<locals>.app at 0x71fba2924220>
  File "/usr/local/lib/python3.11/site-packages/starlette/routing.py", line 73, in app
    response = await f(request)
                     │ └ <starlette.requests.Request object at 0x71fba296c050>
                     └ <function get_request_handler.<locals>.app at 0x71fba2ae80e0>
  File "/usr/local/lib/python3.11/site-packages/fastapi/routing.py", line 301, in app
    raw_response = await run_endpoint_function(
                         └ <function run_endpoint_function at 0x71fba7ecb600>
  File "/usr/local/lib/python3.11/site-packages/fastapi/routing.py", line 212, in run_endpoint_function
    return await dependant.call(**values)
                 │         │      └ {'request': StoryInfoRequest(story_url=HttpUrl('https://webtruyen.diendantruyen.com/truyen/vo-dao-truong-sinh-ta-tu-hanh-co-k...
                 │         └ <function scrape_story_info at 0x71fba317eb60>
                 └ Dependant(path_params=[], query_params=[], header_params=[], cookie_params=[], body_params=[ModelField(field_info=Body(Pydant...

  File "/app/API/routers/scraping.py", line 459, in scrape_story_info
    return await controller.scrape_story_info(request, background_tasks)
                 │          │                 │        └ <fastapi.background.BackgroundTasks object at 0x71fba2952fd0>
                 │          │                 └ StoryInfoRequest(story_url=HttpUrl('https://webtruyen.diendantruyen.com/truyen/vo-dao-truong-sinh-ta-tu-hanh-co-kinh-nghiem')...
                 │          └ <function ScrapingController.scrape_story_info at 0x71fba4f02480>
                 └ <routers.scraping.ScrapingController object at 0x71fba32e89d0>

  File "/app/API/routers/scraping.py", line 54, in scrape_story_info
    story_data = await self.scraping_service.scrape_story_info(
                       │    │                └ <function ScrapingService.scrape_story_info at 0x71fba4f027a0>
                       │    └ <services.scraping_service.ScrapingService object at 0x71fba32eac10>
                       └ <routers.scraping.ScrapingController object at 0x71fba32e89d0>

  File "/app/API/services/scraping_service.py", line 104, in scrape_story_info
    await self._initialize()
          │    └ <function ScrapingService._initialize at 0x71fba4f02660>
          └ <services.scraping_service.ScrapingService object at 0x71fba32eac10>

> File "/app/API/services/scraping_service.py", line 62, in _initialize
    await self._scraper.start()
          │    │        └ <function MetruyenScraper.start at 0x71fb92588cc0>
          │    └ <src.metruyenscraper.MetruyenScraper object at 0x71fba29384d0>
          └ <services.scraping_service.ScrapingService object at 0x71fba32eac10>

  File "/app/Scraper/src/metruyenscraper.py", line 63, in start
    await self.scraper_engine.start()
          │    │              └ <function ScraperEngine.start at 0x71fb92573880>
          │    └ <src.scraper_engine.ScraperEngine object at 0x71fba34ac890>
          └ <src.metruyenscraper.MetruyenScraper object at 0x71fba29384d0>

  File "/app/Scraper/src/scraper_engine.py", line 95, in start
    self.browser = await playwright.chromium.launch(
    │    │               │          └ <property object at 0x71fb925429d0>
    │    │               └ <playwright._impl._playwright.Playwright object at 0x71fb925c4050>
    │    └ None
    └ <src.scraper_engine.ScraperEngine object at 0x71fba34ac890>

  File "/usr/local/lib/python3.11/site-packages/playwright/async_api/_generated.py", line 14438, in launch
    await self._impl_obj.launch(
          │    │         └ <function BrowserType.launch at 0x71fb9251b4c0>
          │    └ <BrowserType name=chromium executable_path=/home/<USER>/.cache/ms-playwright/chromium-1179/chrome-linux/chrome>
          └ <BrowserType name=chromium executable_path=/home/<USER>/.cache/ms-playwright/chromium-1179/chrome-linux/chrome>
  File "/usr/local/lib/python3.11/site-packages/playwright/_impl/_browser_type.py", line 98, in launch
    await self._channel.send(
          │    │        └ <function Channel.send at 0x71fb927b4180>
          │    └ <playwright._impl._connection.Channel object at 0x71fb925a2d10>
          └ <BrowserType name=chromium executable_path=/home/<USER>/.cache/ms-playwright/chromium-1179/chrome-linux/chrome>
  File "/usr/local/lib/python3.11/site-packages/playwright/_impl/_connection.py", line 69, in send
    return await self._connection.wrap_api_call(
                 │    │           └ <function Connection.wrap_api_call at 0x71fb92624d60>
                 │    └ <playwright._impl._connection.Connection object at 0x71fb925a0450>
                 └ <playwright._impl._connection.Channel object at 0x71fb925a2d10>
  File "/usr/local/lib/python3.11/site-packages/playwright/_impl/_connection.py", line 558, in wrap_api_call
    raise rewrite_error(error, f"{parsed_st['apiName']}: {error}") from None
          └ <function rewrite_error at 0x71fb9275c180>

playwright._impl._errors.Error: BrowserType.launch: Executable doesn't exist at /home/<USER>/.cache/ms-playwright/chromium_headless_shell-1179/chrome-linux/headless_shell
╔════════════════════════════════════════════════════════════╗
║ Looks like Playwright was just installed or updated.       ║
║ Please run the following command to download new browsers: ║
║                                                            ║
║     playwright install                                     ║
║                                                            ║
║ <3 Playwright Team                                         ║
╚════════════════════════════════════════════════════════════╝

Traceback (most recent call last):

  File "/app/API/main.py", line 288, in <module>
    uvicorn.run(
    │       └ <function run at 0x71fba308dda0>
    └ <module 'uvicorn' from '/usr/local/lib/python3.11/site-packages/uvicorn/__init__.py'>

  File "/usr/local/lib/python3.11/site-packages/uvicorn/main.py", line 580, in run
    server.run()
    │      └ <function Server.run at 0x71fba308df80>
    └ <uvicorn.server.Server object at 0x71fba4f63d90>
  File "/usr/local/lib/python3.11/site-packages/uvicorn/server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x71fba308e020>
           │       │   └ <uvicorn.server.Server object at 0x71fba4f63d90>
           │       └ <function run at 0x71fba7acd620>
           └ <module 'asyncio' from '/usr/local/lib/python3.11/asyncio/__init__.py'>
  File "/usr/local/lib/python3.11/asyncio/runners.py", line 190, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x71fba30773d0>
           │      └ <function Runner.run at 0x71fba73d0400>
           └ <asyncio.runners.Runner object at 0x71fba32e9910>
  File "/usr/local/lib/python3.11/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at /usr/local/lib/python3.11/site-packages/uvicorn/server.py:71> wai...
           │    │     └ <cyfunction Loop.run_until_complete at 0x71fba305fe00>
           │    └ <uvloop.Loop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x71fba32e9910>
  File "/usr/local/lib/python3.11/site-packages/starlette/middleware/base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x71fba2925f80>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fba2925b20>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │    └ <starlette.middleware.trustedhost.TrustedHostMiddleware object at 0x71fba32e8e50>
          └ <middleware.error_handling.ErrorHandlingMiddleware object at 0x71fba34af090>
  File "/usr/local/lib/python3.11/site-packages/starlette/middleware/trustedhost.py", line 51, in __call__
    await self.app(scope, receive, send)
          │    │   │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x71fba2925f80>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fba2925b20>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │    └ <starlette.middleware.cors.CORSMiddleware object at 0x71fba30cf910>
          └ <starlette.middleware.trustedhost.TrustedHostMiddleware object at 0x71fba32e8e50>
  File "/usr/local/lib/python3.11/site-packages/starlette/middleware/cors.py", line 93, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
          │    │               │      │        │                     └ Headers({'host': 'localhost:8000', 'connection': 'keep-alive', 'content-length': '160', 'sec-ch-ua-platform': '"Windows"', 'u...
          │    │               │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x71fba2925f80>
          │    │               │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fba2925b20>
          │    │               └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │    └ <function CORSMiddleware.simple_response at 0x71fba6552de0>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x71fba30cf910>
  File "/usr/local/lib/python3.11/site-packages/starlette/middleware/cors.py", line 144, in simple_response
    await self.app(scope, receive, send)
          │    │   │      │        └ functools.partial(<bound method CORSMiddleware.send of <starlette.middleware.cors.CORSMiddleware object at 0x71fba30cf910>>, ...
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fba2925b20>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x71fba34a0990>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x71fba30cf910>
  File "/usr/local/lib/python3.11/site-packages/starlette/middleware/exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ functools.partial(<bound method CORSMiddleware.send of <starlette.middleware.cors.CORSMiddleware object at 0x71fba30cf910>>, ...
          │                            │    │    │     │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fba2925b20>
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │                            │    │    └ <starlette.requests.Request object at 0x71fba296e910>
          │                            │    └ <fastapi.routing.APIRouter object at 0x71fba2ad7950>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x71fba34a0990>
          └ <function wrap_app_handling_exceptions at 0x71fba64e1f80>
  File "/usr/local/lib/python3.11/site-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x71fba2925a80>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fba2925b20>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          └ <fastapi.routing.APIRouter object at 0x71fba2ad7950>
  File "/usr/local/lib/python3.11/site-packages/starlette/routing.py", line 714, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x71fba2925a80>
          │    │                │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fba2925b20>
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x71fba2ad7950>>
          └ <fastapi.routing.APIRouter object at 0x71fba2ad7950>
  File "/usr/local/lib/python3.11/site-packages/starlette/routing.py", line 734, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x71fba2925a80>
          │     │      │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fba2925b20>
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │     └ <function Route.handle at 0x71fba64e3560>
          └ APIRoute(path='/api/v1/scraping/story-info', name='scrape_story_info', methods=['POST'])
  File "/usr/local/lib/python3.11/site-packages/starlette/routing.py", line 288, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x71fba2925a80>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fba2925b20>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │    └ <function request_response.<locals>.app at 0x71fba2ae8180>
          └ APIRoute(path='/api/v1/scraping/story-info', name='scrape_story_info', methods=['POST'])
  File "/usr/local/lib/python3.11/site-packages/starlette/routing.py", line 76, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
          │                            │    │        │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x71fba2925a80>
          │                            │    │        │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fba2925b20>
          │                            │    │        └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │                            │    └ <starlette.requests.Request object at 0x71fba296c050>
          │                            └ <function request_response.<locals>.app.<locals>.app at 0x71fba2924220>
          └ <function wrap_app_handling_exceptions at 0x71fba64e1f80>
  File "/usr/local/lib/python3.11/site-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x71fba2925c60>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fba2925b20>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          └ <function request_response.<locals>.app.<locals>.app at 0x71fba2924220>
  File "/usr/local/lib/python3.11/site-packages/starlette/routing.py", line 73, in app
    response = await f(request)
                     │ └ <starlette.requests.Request object at 0x71fba296c050>
                     └ <function get_request_handler.<locals>.app at 0x71fba2ae80e0>
  File "/usr/local/lib/python3.11/site-packages/fastapi/routing.py", line 301, in app
    raw_response = await run_endpoint_function(
                         └ <function run_endpoint_function at 0x71fba7ecb600>
  File "/usr/local/lib/python3.11/site-packages/fastapi/routing.py", line 212, in run_endpoint_function
    return await dependant.call(**values)
                 │         │      └ {'request': StoryInfoRequest(story_url=HttpUrl('https://webtruyen.diendantruyen.com/truyen/vo-dao-truong-sinh-ta-tu-hanh-co-k...
                 │         └ <function scrape_story_info at 0x71fba317eb60>
                 └ Dependant(path_params=[], query_params=[], header_params=[], cookie_params=[], body_params=[ModelField(field_info=Body(Pydant...

  File "/app/API/routers/scraping.py", line 459, in scrape_story_info
    return await controller.scrape_story_info(request, background_tasks)
                 │          │                 │        └ <fastapi.background.BackgroundTasks object at 0x71fba2952fd0>
                 │          │                 └ StoryInfoRequest(story_url=HttpUrl('https://webtruyen.diendantruyen.com/truyen/vo-dao-truong-sinh-ta-tu-hanh-co-kinh-nghiem')...
                 │          └ <function ScrapingController.scrape_story_info at 0x71fba4f02480>
                 └ <routers.scraping.ScrapingController object at 0x71fba32e89d0>

  File "/app/API/routers/scraping.py", line 54, in scrape_story_info
    story_data = await self.scraping_service.scrape_story_info(
                       │    │                └ <function ScrapingService.scrape_story_info at 0x71fba4f027a0>
                       │    └ <services.scraping_service.ScrapingService object at 0x71fba32eac10>
                       └ <routers.scraping.ScrapingController object at 0x71fba32e89d0>

  File "/app/API/services/scraping_service.py", line 104, in scrape_story_info
    await self._initialize()
          │    └ <function ScrapingService._initialize at 0x71fba4f02660>
          └ <services.scraping_service.ScrapingService object at 0x71fba32eac10>

> File "/app/API/services/scraping_service.py", line 62, in _initialize
    await self._scraper.start()
          │    │        └ <function MetruyenScraper.start at 0x71fb92588cc0>
          │    └ <src.metruyenscraper.MetruyenScraper object at 0x71fba29384d0>
          └ <services.scraping_service.ScrapingService object at 0x71fba32eac10>

  File "/app/Scraper/src/metruyenscraper.py", line 63, in start
    await self.scraper_engine.start()
          │    │              └ <function ScraperEngine.start at 0x71fb92573880>
          │    └ <src.scraper_engine.ScraperEngine object at 0x71fba34ac890>
          └ <src.metruyenscraper.MetruyenScraper object at 0x71fba29384d0>

  File "/app/Scraper/src/scraper_engine.py", line 95, in start
    self.browser = await playwright.chromium.launch(
    │    │               │          └ <property object at 0x71fb925429d0>
    │    │               └ <playwright._impl._playwright.Playwright object at 0x71fb925c4050>
    │    └ None
    └ <src.scraper_engine.ScraperEngine object at 0x71fba34ac890>

  File "/usr/local/lib/python3.11/site-packages/playwright/async_api/_generated.py", line 14438, in launch
    await self._impl_obj.launch(
          │    │         └ <function BrowserType.launch at 0x71fb9251b4c0>
          │    └ <BrowserType name=chromium executable_path=/home/<USER>/.cache/ms-playwright/chromium-1179/chrome-linux/chrome>
          └ <BrowserType name=chromium executable_path=/home/<USER>/.cache/ms-playwright/chromium-1179/chrome-linux/chrome>
  File "/usr/local/lib/python3.11/site-packages/playwright/_impl/_browser_type.py", line 98, in launch
    await self._channel.send(
          │    │        └ <function Channel.send at 0x71fb927b4180>
          │    └ <playwright._impl._connection.Channel object at 0x71fb925a2d10>
          └ <BrowserType name=chromium executable_path=/home/<USER>/.cache/ms-playwright/chromium-1179/chrome-linux/chrome>
  File "/usr/local/lib/python3.11/site-packages/playwright/_impl/_connection.py", line 69, in send
    return await self._connection.wrap_api_call(
                 │    │           └ <function Connection.wrap_api_call at 0x71fb92624d60>
                 │    └ <playwright._impl._connection.Connection object at 0x71fb925a0450>
                 └ <playwright._impl._connection.Channel object at 0x71fb925a2d10>
  File "/usr/local/lib/python3.11/site-packages/playwright/_impl/_connection.py", line 558, in wrap_api_call
    raise rewrite_error(error, f"{parsed_st['apiName']}: {error}") from None
          └ <function rewrite_error at 0x71fb9275c180>

playwright._impl._errors.Error: BrowserType.launch: Executable doesn't exist at /home/<USER>/.cache/ms-playwright/chromium_headless_shell-1179/chrome-linux/headless_shell
╔════════════════════════════════════════════════════════════╗
║ Looks like Playwright was just installed or updated.       ║
║ Please run the following command to download new browsers: ║
║                                                            ║
║     playwright install                                     ║
║                                                            ║
║ <3 Playwright Team                                         ║
╚════════════════════════════════════════════════════════════╝
2025-07-09 08:46:37 | ERROR    | logging:callHandlers:1706 | Error scraping story info: Scraping service initialization failed: BrowserType.launch: Executable doesn't exist at /home/<USER>/.cache/ms-playwright/chromium_headless_shell-1179/chrome-linux/headless_shell
╔════════════════════════════════════════════════════════════╗
║ Looks like Playwright was just installed or updated.       ║
║ Please run the following command to download new browsers: ║
║                                                            ║
║     playwright install                                     ║
║                                                            ║
║ <3 Playwright Team                                         ║
╚════════════════════════════════════════════════════════════╝
Traceback (most recent call last):

  File "/app/API/services/scraping_service.py", line 62, in _initialize
    await self._scraper.start()
          │    │        └ <function MetruyenScraper.start at 0x71fb92588cc0>
          │    └ <src.metruyenscraper.MetruyenScraper object at 0x71fba29384d0>
          └ <services.scraping_service.ScrapingService object at 0x71fba32eac10>

  File "/app/Scraper/src/metruyenscraper.py", line 63, in start
    await self.scraper_engine.start()
          │    │              └ <function ScraperEngine.start at 0x71fb92573880>
          │    └ <src.scraper_engine.ScraperEngine object at 0x71fba34ac890>
          └ <src.metruyenscraper.MetruyenScraper object at 0x71fba29384d0>

  File "/app/Scraper/src/scraper_engine.py", line 95, in start
    self.browser = await playwright.chromium.launch(
    │    │               │          └ <property object at 0x71fb925429d0>
    │    │               └ <playwright._impl._playwright.Playwright object at 0x71fb925c4050>
    │    └ None
    └ <src.scraper_engine.ScraperEngine object at 0x71fba34ac890>

  File "/usr/local/lib/python3.11/site-packages/playwright/async_api/_generated.py", line 14438, in launch
    await self._impl_obj.launch(
          │    │         └ <function BrowserType.launch at 0x71fb9251b4c0>
          │    └ <BrowserType name=chromium executable_path=/home/<USER>/.cache/ms-playwright/chromium-1179/chrome-linux/chrome>
          └ <BrowserType name=chromium executable_path=/home/<USER>/.cache/ms-playwright/chromium-1179/chrome-linux/chrome>
  File "/usr/local/lib/python3.11/site-packages/playwright/_impl/_browser_type.py", line 98, in launch
    await self._channel.send(
          │    │        └ <function Channel.send at 0x71fb927b4180>
          │    └ <playwright._impl._connection.Channel object at 0x71fb925a2d10>
          └ <BrowserType name=chromium executable_path=/home/<USER>/.cache/ms-playwright/chromium-1179/chrome-linux/chrome>
  File "/usr/local/lib/python3.11/site-packages/playwright/_impl/_connection.py", line 69, in send
    return await self._connection.wrap_api_call(
                 │    │           └ <function Connection.wrap_api_call at 0x71fb92624d60>
                 │    └ <playwright._impl._connection.Connection object at 0x71fb925a0450>
                 └ <playwright._impl._connection.Channel object at 0x71fb925a2d10>
  File "/usr/local/lib/python3.11/site-packages/playwright/_impl/_connection.py", line 558, in wrap_api_call
    raise rewrite_error(error, f"{parsed_st['apiName']}: {error}") from None
          └ <function rewrite_error at 0x71fb9275c180>

playwright._impl._errors.Error: BrowserType.launch: Executable doesn't exist at /home/<USER>/.cache/ms-playwright/chromium_headless_shell-1179/chrome-linux/headless_shell
╔════════════════════════════════════════════════════════════╗
║ Looks like Playwright was just installed or updated.       ║
║ Please run the following command to download new browsers: ║
║                                                            ║
║     playwright install                                     ║
║                                                            ║
║ <3 Playwright Team                                         ║
╚════════════════════════════════════════════════════════════╝


During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "/app/API/main.py", line 288, in <module>
    uvicorn.run(
    │       └ <function run at 0x71fba308dda0>
    └ <module 'uvicorn' from '/usr/local/lib/python3.11/site-packages/uvicorn/__init__.py'>

  File "/usr/local/lib/python3.11/site-packages/uvicorn/main.py", line 580, in run
    server.run()
    │      └ <function Server.run at 0x71fba308df80>
    └ <uvicorn.server.Server object at 0x71fba4f63d90>
  File "/usr/local/lib/python3.11/site-packages/uvicorn/server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x71fba308e020>
           │       │   └ <uvicorn.server.Server object at 0x71fba4f63d90>
           │       └ <function run at 0x71fba7acd620>
           └ <module 'asyncio' from '/usr/local/lib/python3.11/asyncio/__init__.py'>
  File "/usr/local/lib/python3.11/asyncio/runners.py", line 190, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x71fba30773d0>
           │      └ <function Runner.run at 0x71fba73d0400>
           └ <asyncio.runners.Runner object at 0x71fba32e9910>
  File "/usr/local/lib/python3.11/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at /usr/local/lib/python3.11/site-packages/uvicorn/server.py:71> wai...
           │    │     └ <cyfunction Loop.run_until_complete at 0x71fba305fe00>
           │    └ <uvloop.Loop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x71fba32e9910>
  File "/usr/local/lib/python3.11/site-packages/starlette/middleware/base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x71fba2925f80>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fba2925b20>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │    └ <starlette.middleware.trustedhost.TrustedHostMiddleware object at 0x71fba32e8e50>
          └ <middleware.error_handling.ErrorHandlingMiddleware object at 0x71fba34af090>
  File "/usr/local/lib/python3.11/site-packages/starlette/middleware/trustedhost.py", line 51, in __call__
    await self.app(scope, receive, send)
          │    │   │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x71fba2925f80>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fba2925b20>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │    └ <starlette.middleware.cors.CORSMiddleware object at 0x71fba30cf910>
          └ <starlette.middleware.trustedhost.TrustedHostMiddleware object at 0x71fba32e8e50>
  File "/usr/local/lib/python3.11/site-packages/starlette/middleware/cors.py", line 93, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
          │    │               │      │        │                     └ Headers({'host': 'localhost:8000', 'connection': 'keep-alive', 'content-length': '160', 'sec-ch-ua-platform': '"Windows"', 'u...
          │    │               │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x71fba2925f80>
          │    │               │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fba2925b20>
          │    │               └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │    └ <function CORSMiddleware.simple_response at 0x71fba6552de0>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x71fba30cf910>
  File "/usr/local/lib/python3.11/site-packages/starlette/middleware/cors.py", line 144, in simple_response
    await self.app(scope, receive, send)
          │    │   │      │        └ functools.partial(<bound method CORSMiddleware.send of <starlette.middleware.cors.CORSMiddleware object at 0x71fba30cf910>>, ...
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fba2925b20>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x71fba34a0990>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x71fba30cf910>
  File "/usr/local/lib/python3.11/site-packages/starlette/middleware/exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ functools.partial(<bound method CORSMiddleware.send of <starlette.middleware.cors.CORSMiddleware object at 0x71fba30cf910>>, ...
          │                            │    │    │     │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fba2925b20>
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │                            │    │    └ <starlette.requests.Request object at 0x71fba296e910>
          │                            │    └ <fastapi.routing.APIRouter object at 0x71fba2ad7950>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x71fba34a0990>
          └ <function wrap_app_handling_exceptions at 0x71fba64e1f80>
  File "/usr/local/lib/python3.11/site-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x71fba2925a80>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fba2925b20>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          └ <fastapi.routing.APIRouter object at 0x71fba2ad7950>
  File "/usr/local/lib/python3.11/site-packages/starlette/routing.py", line 714, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x71fba2925a80>
          │    │                │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fba2925b20>
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x71fba2ad7950>>
          └ <fastapi.routing.APIRouter object at 0x71fba2ad7950>
  File "/usr/local/lib/python3.11/site-packages/starlette/routing.py", line 734, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x71fba2925a80>
          │     │      │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fba2925b20>
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │     └ <function Route.handle at 0x71fba64e3560>
          └ APIRoute(path='/api/v1/scraping/story-info', name='scrape_story_info', methods=['POST'])
  File "/usr/local/lib/python3.11/site-packages/starlette/routing.py", line 288, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x71fba2925a80>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fba2925b20>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │    └ <function request_response.<locals>.app at 0x71fba2ae8180>
          └ APIRoute(path='/api/v1/scraping/story-info', name='scrape_story_info', methods=['POST'])
  File "/usr/local/lib/python3.11/site-packages/starlette/routing.py", line 76, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
          │                            │    │        │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x71fba2925a80>
          │                            │    │        │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fba2925b20>
          │                            │    │        └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │                            │    └ <starlette.requests.Request object at 0x71fba296c050>
          │                            └ <function request_response.<locals>.app.<locals>.app at 0x71fba2924220>
          └ <function wrap_app_handling_exceptions at 0x71fba64e1f80>
  File "/usr/local/lib/python3.11/site-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x71fba2925c60>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fba2925b20>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          └ <function request_response.<locals>.app.<locals>.app at 0x71fba2924220>
  File "/usr/local/lib/python3.11/site-packages/starlette/routing.py", line 73, in app
    response = await f(request)
                     │ └ <starlette.requests.Request object at 0x71fba296c050>
                     └ <function get_request_handler.<locals>.app at 0x71fba2ae80e0>
  File "/usr/local/lib/python3.11/site-packages/fastapi/routing.py", line 301, in app
    raw_response = await run_endpoint_function(
                         └ <function run_endpoint_function at 0x71fba7ecb600>
  File "/usr/local/lib/python3.11/site-packages/fastapi/routing.py", line 212, in run_endpoint_function
    return await dependant.call(**values)
                 │         │      └ {'request': StoryInfoRequest(story_url=HttpUrl('https://webtruyen.diendantruyen.com/truyen/vo-dao-truong-sinh-ta-tu-hanh-co-k...
                 │         └ <function scrape_story_info at 0x71fba317eb60>
                 └ Dependant(path_params=[], query_params=[], header_params=[], cookie_params=[], body_params=[ModelField(field_info=Body(Pydant...

  File "/app/API/routers/scraping.py", line 459, in scrape_story_info
    return await controller.scrape_story_info(request, background_tasks)
                 │          │                 │        └ <fastapi.background.BackgroundTasks object at 0x71fba2952fd0>
                 │          │                 └ StoryInfoRequest(story_url=HttpUrl('https://webtruyen.diendantruyen.com/truyen/vo-dao-truong-sinh-ta-tu-hanh-co-kinh-nghiem')...
                 │          └ <function ScrapingController.scrape_story_info at 0x71fba4f02480>
                 └ <routers.scraping.ScrapingController object at 0x71fba32e89d0>

> File "/app/API/routers/scraping.py", line 54, in scrape_story_info
    story_data = await self.scraping_service.scrape_story_info(
                       │    │                └ <function ScrapingService.scrape_story_info at 0x71fba4f027a0>
                       │    └ <services.scraping_service.ScrapingService object at 0x71fba32eac10>
                       └ <routers.scraping.ScrapingController object at 0x71fba32e89d0>

  File "/app/API/services/scraping_service.py", line 104, in scrape_story_info
    await self._initialize()
          │    └ <function ScrapingService._initialize at 0x71fba4f02660>
          └ <services.scraping_service.ScrapingService object at 0x71fba32eac10>

  File "/app/API/services/scraping_service.py", line 73, in _initialize
    raise ScrapingError(f"Scraping service initialization failed: {str(e) if e else 'Unknown error'}")
          └ <class 'middleware.error_handling.ScrapingError'>

middleware.error_handling.ScrapingError: Scraping service initialization failed: BrowserType.launch: Executable doesn't exist at /home/<USER>/.cache/ms-playwright/chromium_headless_shell-1179/chrome-linux/headless_shell
╔════════════════════════════════════════════════════════════╗
║ Looks like Playwright was just installed or updated.       ║
║ Please run the following command to download new browsers: ║
║                                                            ║
║     playwright install                                     ║
║                                                            ║
║ <3 Playwright Team                                         ║
╚════════════════════════════════════════════════════════════╝

Traceback (most recent call last):

  File "/app/API/services/scraping_service.py", line 62, in _initialize
    await self._scraper.start()
          │    │        └ <function MetruyenScraper.start at 0x71fb92588cc0>
          │    └ <src.metruyenscraper.MetruyenScraper object at 0x71fba29384d0>
          └ <services.scraping_service.ScrapingService object at 0x71fba32eac10>

  File "/app/Scraper/src/metruyenscraper.py", line 63, in start
    await self.scraper_engine.start()
          │    │              └ <function ScraperEngine.start at 0x71fb92573880>
          │    └ <src.scraper_engine.ScraperEngine object at 0x71fba34ac890>
          └ <src.metruyenscraper.MetruyenScraper object at 0x71fba29384d0>

  File "/app/Scraper/src/scraper_engine.py", line 95, in start
    self.browser = await playwright.chromium.launch(
    │    │               │          └ <property object at 0x71fb925429d0>
    │    │               └ <playwright._impl._playwright.Playwright object at 0x71fb925c4050>
    │    └ None
    └ <src.scraper_engine.ScraperEngine object at 0x71fba34ac890>

  File "/usr/local/lib/python3.11/site-packages/playwright/async_api/_generated.py", line 14438, in launch
    await self._impl_obj.launch(
          │    │         └ <function BrowserType.launch at 0x71fb9251b4c0>
          │    └ <BrowserType name=chromium executable_path=/home/<USER>/.cache/ms-playwright/chromium-1179/chrome-linux/chrome>
          └ <BrowserType name=chromium executable_path=/home/<USER>/.cache/ms-playwright/chromium-1179/chrome-linux/chrome>
  File "/usr/local/lib/python3.11/site-packages/playwright/_impl/_browser_type.py", line 98, in launch
    await self._channel.send(
          │    │        └ <function Channel.send at 0x71fb927b4180>
          │    └ <playwright._impl._connection.Channel object at 0x71fb925a2d10>
          └ <BrowserType name=chromium executable_path=/home/<USER>/.cache/ms-playwright/chromium-1179/chrome-linux/chrome>
  File "/usr/local/lib/python3.11/site-packages/playwright/_impl/_connection.py", line 69, in send
    return await self._connection.wrap_api_call(
                 │    │           └ <function Connection.wrap_api_call at 0x71fb92624d60>
                 │    └ <playwright._impl._connection.Connection object at 0x71fb925a0450>
                 └ <playwright._impl._connection.Channel object at 0x71fb925a2d10>
  File "/usr/local/lib/python3.11/site-packages/playwright/_impl/_connection.py", line 558, in wrap_api_call
    raise rewrite_error(error, f"{parsed_st['apiName']}: {error}") from None
          └ <function rewrite_error at 0x71fb9275c180>

playwright._impl._errors.Error: BrowserType.launch: Executable doesn't exist at /home/<USER>/.cache/ms-playwright/chromium_headless_shell-1179/chrome-linux/headless_shell
╔════════════════════════════════════════════════════════════╗
║ Looks like Playwright was just installed or updated.       ║
║ Please run the following command to download new browsers: ║
║                                                            ║
║     playwright install                                     ║
║                                                            ║
║ <3 Playwright Team                                         ║
╚════════════════════════════════════════════════════════════╝


During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "/app/API/main.py", line 288, in <module>
    uvicorn.run(
    │       └ <function run at 0x71fba308dda0>
    └ <module 'uvicorn' from '/usr/local/lib/python3.11/site-packages/uvicorn/__init__.py'>

  File "/usr/local/lib/python3.11/site-packages/uvicorn/main.py", line 580, in run
    server.run()
    │      └ <function Server.run at 0x71fba308df80>
    └ <uvicorn.server.Server object at 0x71fba4f63d90>
  File "/usr/local/lib/python3.11/site-packages/uvicorn/server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x71fba308e020>
           │       │   └ <uvicorn.server.Server object at 0x71fba4f63d90>
           │       └ <function run at 0x71fba7acd620>
           └ <module 'asyncio' from '/usr/local/lib/python3.11/asyncio/__init__.py'>
  File "/usr/local/lib/python3.11/asyncio/runners.py", line 190, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x71fba30773d0>
           │      └ <function Runner.run at 0x71fba73d0400>
           └ <asyncio.runners.Runner object at 0x71fba32e9910>
  File "/usr/local/lib/python3.11/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at /usr/local/lib/python3.11/site-packages/uvicorn/server.py:71> wai...
           │    │     └ <cyfunction Loop.run_until_complete at 0x71fba305fe00>
           │    └ <uvloop.Loop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x71fba32e9910>
  File "/usr/local/lib/python3.11/site-packages/starlette/middleware/base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x71fba2925f80>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fba2925b20>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │    └ <starlette.middleware.trustedhost.TrustedHostMiddleware object at 0x71fba32e8e50>
          └ <middleware.error_handling.ErrorHandlingMiddleware object at 0x71fba34af090>
  File "/usr/local/lib/python3.11/site-packages/starlette/middleware/trustedhost.py", line 51, in __call__
    await self.app(scope, receive, send)
          │    │   │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x71fba2925f80>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fba2925b20>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │    └ <starlette.middleware.cors.CORSMiddleware object at 0x71fba30cf910>
          └ <starlette.middleware.trustedhost.TrustedHostMiddleware object at 0x71fba32e8e50>
  File "/usr/local/lib/python3.11/site-packages/starlette/middleware/cors.py", line 93, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
          │    │               │      │        │                     └ Headers({'host': 'localhost:8000', 'connection': 'keep-alive', 'content-length': '160', 'sec-ch-ua-platform': '"Windows"', 'u...
          │    │               │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x71fba2925f80>
          │    │               │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fba2925b20>
          │    │               └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │    └ <function CORSMiddleware.simple_response at 0x71fba6552de0>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x71fba30cf910>
  File "/usr/local/lib/python3.11/site-packages/starlette/middleware/cors.py", line 144, in simple_response
    await self.app(scope, receive, send)
          │    │   │      │        └ functools.partial(<bound method CORSMiddleware.send of <starlette.middleware.cors.CORSMiddleware object at 0x71fba30cf910>>, ...
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fba2925b20>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x71fba34a0990>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x71fba30cf910>
  File "/usr/local/lib/python3.11/site-packages/starlette/middleware/exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ functools.partial(<bound method CORSMiddleware.send of <starlette.middleware.cors.CORSMiddleware object at 0x71fba30cf910>>, ...
          │                            │    │    │     │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fba2925b20>
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │                            │    │    └ <starlette.requests.Request object at 0x71fba296e910>
          │                            │    └ <fastapi.routing.APIRouter object at 0x71fba2ad7950>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x71fba34a0990>
          └ <function wrap_app_handling_exceptions at 0x71fba64e1f80>
  File "/usr/local/lib/python3.11/site-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x71fba2925a80>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fba2925b20>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          └ <fastapi.routing.APIRouter object at 0x71fba2ad7950>
  File "/usr/local/lib/python3.11/site-packages/starlette/routing.py", line 714, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x71fba2925a80>
          │    │                │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fba2925b20>
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x71fba2ad7950>>
          └ <fastapi.routing.APIRouter object at 0x71fba2ad7950>
  File "/usr/local/lib/python3.11/site-packages/starlette/routing.py", line 734, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x71fba2925a80>
          │     │      │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fba2925b20>
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │     └ <function Route.handle at 0x71fba64e3560>
          └ APIRoute(path='/api/v1/scraping/story-info', name='scrape_story_info', methods=['POST'])
  File "/usr/local/lib/python3.11/site-packages/starlette/routing.py", line 288, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x71fba2925a80>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fba2925b20>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │    └ <function request_response.<locals>.app at 0x71fba2ae8180>
          └ APIRoute(path='/api/v1/scraping/story-info', name='scrape_story_info', methods=['POST'])
  File "/usr/local/lib/python3.11/site-packages/starlette/routing.py", line 76, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
          │                            │    │        │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x71fba2925a80>
          │                            │    │        │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fba2925b20>
          │                            │    │        └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │                            │    └ <starlette.requests.Request object at 0x71fba296c050>
          │                            └ <function request_response.<locals>.app.<locals>.app at 0x71fba2924220>
          └ <function wrap_app_handling_exceptions at 0x71fba64e1f80>
  File "/usr/local/lib/python3.11/site-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x71fba2925c60>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fba2925b20>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          └ <function request_response.<locals>.app.<locals>.app at 0x71fba2924220>
  File "/usr/local/lib/python3.11/site-packages/starlette/routing.py", line 73, in app
    response = await f(request)
                     │ └ <starlette.requests.Request object at 0x71fba296c050>
                     └ <function get_request_handler.<locals>.app at 0x71fba2ae80e0>
  File "/usr/local/lib/python3.11/site-packages/fastapi/routing.py", line 301, in app
    raw_response = await run_endpoint_function(
                         └ <function run_endpoint_function at 0x71fba7ecb600>
  File "/usr/local/lib/python3.11/site-packages/fastapi/routing.py", line 212, in run_endpoint_function
    return await dependant.call(**values)
                 │         │      └ {'request': StoryInfoRequest(story_url=HttpUrl('https://webtruyen.diendantruyen.com/truyen/vo-dao-truong-sinh-ta-tu-hanh-co-k...
                 │         └ <function scrape_story_info at 0x71fba317eb60>
                 └ Dependant(path_params=[], query_params=[], header_params=[], cookie_params=[], body_params=[ModelField(field_info=Body(Pydant...

  File "/app/API/routers/scraping.py", line 459, in scrape_story_info
    return await controller.scrape_story_info(request, background_tasks)
                 │          │                 │        └ <fastapi.background.BackgroundTasks object at 0x71fba2952fd0>
                 │          │                 └ StoryInfoRequest(story_url=HttpUrl('https://webtruyen.diendantruyen.com/truyen/vo-dao-truong-sinh-ta-tu-hanh-co-kinh-nghiem')...
                 │          └ <function ScrapingController.scrape_story_info at 0x71fba4f02480>
                 └ <routers.scraping.ScrapingController object at 0x71fba32e89d0>

> File "/app/API/routers/scraping.py", line 54, in scrape_story_info
    story_data = await self.scraping_service.scrape_story_info(
                       │    │                └ <function ScrapingService.scrape_story_info at 0x71fba4f027a0>
                       │    └ <services.scraping_service.ScrapingService object at 0x71fba32eac10>
                       └ <routers.scraping.ScrapingController object at 0x71fba32e89d0>

  File "/app/API/services/scraping_service.py", line 104, in scrape_story_info
    await self._initialize()
          │    └ <function ScrapingService._initialize at 0x71fba4f02660>
          └ <services.scraping_service.ScrapingService object at 0x71fba32eac10>

  File "/app/API/services/scraping_service.py", line 73, in _initialize
    raise ScrapingError(f"Scraping service initialization failed: {str(e) if e else 'Unknown error'}")
          └ <class 'middleware.error_handling.ScrapingError'>

middleware.error_handling.ScrapingError: Scraping service initialization failed: BrowserType.launch: Executable doesn't exist at /home/<USER>/.cache/ms-playwright/chromium_headless_shell-1179/chrome-linux/headless_shell
╔════════════════════════════════════════════════════════════╗
║ Looks like Playwright was just installed or updated.       ║
║ Please run the following command to download new browsers: ║
║                                                            ║
║     playwright install                                     ║
║                                                            ║
║ <3 Playwright Team                                         ║
╚════════════════════════════════════════════════════════════╝
2025-07-09 08:46:37 | ERROR    | logging:callHandlers:1706 | Scraping Error: Failed to scrape story information: Scraping service initialization failed: BrowserType.launch: Executable doesn't exist at /home/<USER>/.cache/ms-playwright/chromium_headless_shell-1179/chrome-linux/headless_shell
╔════════════════════════════════════════════════════════════╗
║ Looks like Playwright was just installed or updated.       ║
║ Please run the following command to download new browsers: ║
║                                                            ║
║     playwright install                                     ║
║                                                            ║
║ <3 Playwright Team                                         ║
╚════════════════════════════════════════════════════════════╝ - Path: /api/v1/scraping/story-info - Method: POST
Traceback (most recent call last):

  File "/usr/local/lib/python3.11/site-packages/starlette/middleware/base.py", line 148, in call_next
    message = await recv_stream.receive()
                    │           └ <function MemoryObjectReceiveStream.receive at 0x71fba6723d80>
                    └ MemoryObjectReceiveStream(_state=MemoryObjectStreamState(max_buffer_size=0, buffer=deque([]), open_send_channels=0, open_rece...
  File "/usr/local/lib/python3.11/site-packages/anyio/streams/memory.py", line 126, in receive
    raise EndOfStream from None
          └ <class 'anyio.EndOfStream'>

anyio.EndOfStream


During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "/app/API/main.py", line 288, in <module>
    uvicorn.run(
    │       └ <function run at 0x71fba308dda0>
    └ <module 'uvicorn' from '/usr/local/lib/python3.11/site-packages/uvicorn/__init__.py'>

  File "/usr/local/lib/python3.11/site-packages/uvicorn/main.py", line 580, in run
    server.run()
    │      └ <function Server.run at 0x71fba308df80>
    └ <uvicorn.server.Server object at 0x71fba4f63d90>
  File "/usr/local/lib/python3.11/site-packages/uvicorn/server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x71fba308e020>
           │       │   └ <uvicorn.server.Server object at 0x71fba4f63d90>
           │       └ <function run at 0x71fba7acd620>
           └ <module 'asyncio' from '/usr/local/lib/python3.11/asyncio/__init__.py'>
  File "/usr/local/lib/python3.11/asyncio/runners.py", line 190, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x71fba30773d0>
           │      └ <function Runner.run at 0x71fba73d0400>
           └ <asyncio.runners.Runner object at 0x71fba32e9910>
  File "/usr/local/lib/python3.11/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at /usr/local/lib/python3.11/site-packages/uvicorn/server.py:71> wai...
           │    │     └ <cyfunction Loop.run_until_complete at 0x71fba305fe00>
           │    └ <uvloop.Loop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x71fba32e9910>
  File "/usr/local/lib/python3.11/site-packages/starlette/middleware/base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x71fba29265c0>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fba2926200>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │    └ <middleware.error_handling.ErrorHandlingMiddleware object at 0x71fba34af090>
          └ <middleware.rate_limiting.RateLimitingMiddleware object at 0x71fba2b17c50>
  File "/usr/local/lib/python3.11/site-packages/starlette/middleware/base.py", line 178, in __call__
    response = await self.dispatch_func(request, call_next)
                     │    │             │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x71fba2925da0>
                     │    │             └ <starlette.middleware.base._CachedRequest object at 0x71fba296f290>
                     │    └ <bound method ErrorHandlingMiddleware.dispatch of <middleware.error_handling.ErrorHandlingMiddleware object at 0x71fba34af090>>
                     └ <middleware.error_handling.ErrorHandlingMiddleware object at 0x71fba34af090>

> File "/app/API/middleware/error_handling.py", line 31, in dispatch
    response = await call_next(request)
                     │         └ <starlette.middleware.base._CachedRequest object at 0x71fba296f290>
                     └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x71fba2925da0>

  File "/usr/local/lib/python3.11/site-packages/starlette/middleware/base.py", line 156, in call_next
    raise app_exc
          └ ScrapingError("Failed to scrape story information: Scraping service initialization failed: BrowserType.launch: Executable doe...
  File "/usr/local/lib/python3.11/site-packages/starlette/middleware/base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x71fba2925f80>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fba2925b20>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │    └ <starlette.middleware.trustedhost.TrustedHostMiddleware object at 0x71fba32e8e50>
          └ <middleware.error_handling.ErrorHandlingMiddleware object at 0x71fba34af090>
  File "/usr/local/lib/python3.11/site-packages/starlette/middleware/trustedhost.py", line 51, in __call__
    await self.app(scope, receive, send)
          │    │   │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x71fba2925f80>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fba2925b20>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │    └ <starlette.middleware.cors.CORSMiddleware object at 0x71fba30cf910>
          └ <starlette.middleware.trustedhost.TrustedHostMiddleware object at 0x71fba32e8e50>
  File "/usr/local/lib/python3.11/site-packages/starlette/middleware/cors.py", line 93, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
          │    │               │      │        │                     └ Headers({'host': 'localhost:8000', 'connection': 'keep-alive', 'content-length': '160', 'sec-ch-ua-platform': '"Windows"', 'u...
          │    │               │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x71fba2925f80>
          │    │               │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fba2925b20>
          │    │               └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │    └ <function CORSMiddleware.simple_response at 0x71fba6552de0>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x71fba30cf910>
  File "/usr/local/lib/python3.11/site-packages/starlette/middleware/cors.py", line 144, in simple_response
    await self.app(scope, receive, send)
          │    │   │      │        └ functools.partial(<bound method CORSMiddleware.send of <starlette.middleware.cors.CORSMiddleware object at 0x71fba30cf910>>, ...
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fba2925b20>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x71fba34a0990>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x71fba30cf910>
  File "/usr/local/lib/python3.11/site-packages/starlette/middleware/exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ functools.partial(<bound method CORSMiddleware.send of <starlette.middleware.cors.CORSMiddleware object at 0x71fba30cf910>>, ...
          │                            │    │    │     │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fba2925b20>
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │                            │    │    └ <starlette.requests.Request object at 0x71fba296e910>
          │                            │    └ <fastapi.routing.APIRouter object at 0x71fba2ad7950>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x71fba34a0990>
          └ <function wrap_app_handling_exceptions at 0x71fba64e1f80>
  File "/usr/local/lib/python3.11/site-packages/starlette/_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "/usr/local/lib/python3.11/site-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x71fba2925a80>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fba2925b20>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          └ <fastapi.routing.APIRouter object at 0x71fba2ad7950>
  File "/usr/local/lib/python3.11/site-packages/starlette/routing.py", line 714, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x71fba2925a80>
          │    │                │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fba2925b20>
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x71fba2ad7950>>
          └ <fastapi.routing.APIRouter object at 0x71fba2ad7950>
  File "/usr/local/lib/python3.11/site-packages/starlette/routing.py", line 734, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x71fba2925a80>
          │     │      │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fba2925b20>
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │     └ <function Route.handle at 0x71fba64e3560>
          └ APIRoute(path='/api/v1/scraping/story-info', name='scrape_story_info', methods=['POST'])
  File "/usr/local/lib/python3.11/site-packages/starlette/routing.py", line 288, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x71fba2925a80>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fba2925b20>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │    └ <function request_response.<locals>.app at 0x71fba2ae8180>
          └ APIRoute(path='/api/v1/scraping/story-info', name='scrape_story_info', methods=['POST'])
  File "/usr/local/lib/python3.11/site-packages/starlette/routing.py", line 76, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
          │                            │    │        │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x71fba2925a80>
          │                            │    │        │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fba2925b20>
          │                            │    │        └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │                            │    └ <starlette.requests.Request object at 0x71fba296c050>
          │                            └ <function request_response.<locals>.app.<locals>.app at 0x71fba2924220>
          └ <function wrap_app_handling_exceptions at 0x71fba64e1f80>
  File "/usr/local/lib/python3.11/site-packages/starlette/_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "/usr/local/lib/python3.11/site-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x71fba2925c60>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fba2925b20>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          └ <function request_response.<locals>.app.<locals>.app at 0x71fba2924220>
  File "/usr/local/lib/python3.11/site-packages/starlette/routing.py", line 73, in app
    response = await f(request)
                     │ └ <starlette.requests.Request object at 0x71fba296c050>
                     └ <function get_request_handler.<locals>.app at 0x71fba2ae80e0>
  File "/usr/local/lib/python3.11/site-packages/fastapi/routing.py", line 301, in app
    raw_response = await run_endpoint_function(
                         └ <function run_endpoint_function at 0x71fba7ecb600>
  File "/usr/local/lib/python3.11/site-packages/fastapi/routing.py", line 212, in run_endpoint_function
    return await dependant.call(**values)
                 │         │      └ {'request': StoryInfoRequest(story_url=HttpUrl('https://webtruyen.diendantruyen.com/truyen/vo-dao-truong-sinh-ta-tu-hanh-co-k...
                 │         └ <function scrape_story_info at 0x71fba317eb60>
                 └ Dependant(path_params=[], query_params=[], header_params=[], cookie_params=[], body_params=[ModelField(field_info=Body(Pydant...

  File "/app/API/routers/scraping.py", line 459, in scrape_story_info
    return await controller.scrape_story_info(request, background_tasks)
                 │          │                 │        └ <fastapi.background.BackgroundTasks object at 0x71fba2952fd0>
                 │          │                 └ StoryInfoRequest(story_url=HttpUrl('https://webtruyen.diendantruyen.com/truyen/vo-dao-truong-sinh-ta-tu-hanh-co-kinh-nghiem')...
                 │          └ <function ScrapingController.scrape_story_info at 0x71fba4f02480>
                 └ <routers.scraping.ScrapingController object at 0x71fba32e89d0>

  File "/app/API/routers/scraping.py", line 108, in scrape_story_info
    raise ScrapingError(f"Failed to scrape story information: {e}")
          └ <class 'middleware.error_handling.ScrapingError'>

middleware.error_handling.ScrapingError: Failed to scrape story information: Scraping service initialization failed: BrowserType.launch: Executable doesn't exist at /home/<USER>/.cache/ms-playwright/chromium_headless_shell-1179/chrome-linux/headless_shell
╔════════════════════════════════════════════════════════════╗
║ Looks like Playwright was just installed or updated.       ║
║ Please run the following command to download new browsers: ║
║                                                            ║
║     playwright install                                     ║
║                                                            ║
║ <3 Playwright Team                                         ║
╚════════════════════════════════════════════════════════════╝

Traceback (most recent call last):

  File "/usr/local/lib/python3.11/site-packages/starlette/middleware/base.py", line 148, in call_next
    message = await recv_stream.receive()
                    │           └ <function MemoryObjectReceiveStream.receive at 0x71fba6723d80>
                    └ MemoryObjectReceiveStream(_state=MemoryObjectStreamState(max_buffer_size=0, buffer=deque([]), open_send_channels=0, open_rece...
  File "/usr/local/lib/python3.11/site-packages/anyio/streams/memory.py", line 126, in receive
    raise EndOfStream from None
          └ <class 'anyio.EndOfStream'>

anyio.EndOfStream


During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "/app/API/main.py", line 288, in <module>
    uvicorn.run(
    │       └ <function run at 0x71fba308dda0>
    └ <module 'uvicorn' from '/usr/local/lib/python3.11/site-packages/uvicorn/__init__.py'>

  File "/usr/local/lib/python3.11/site-packages/uvicorn/main.py", line 580, in run
    server.run()
    │      └ <function Server.run at 0x71fba308df80>
    └ <uvicorn.server.Server object at 0x71fba4f63d90>
  File "/usr/local/lib/python3.11/site-packages/uvicorn/server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x71fba308e020>
           │       │   └ <uvicorn.server.Server object at 0x71fba4f63d90>
           │       └ <function run at 0x71fba7acd620>
           └ <module 'asyncio' from '/usr/local/lib/python3.11/asyncio/__init__.py'>
  File "/usr/local/lib/python3.11/asyncio/runners.py", line 190, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x71fba30773d0>
           │      └ <function Runner.run at 0x71fba73d0400>
           └ <asyncio.runners.Runner object at 0x71fba32e9910>
  File "/usr/local/lib/python3.11/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at /usr/local/lib/python3.11/site-packages/uvicorn/server.py:71> wai...
           │    │     └ <cyfunction Loop.run_until_complete at 0x71fba305fe00>
           │    └ <uvloop.Loop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x71fba32e9910>
  File "/usr/local/lib/python3.11/site-packages/starlette/middleware/base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x71fba29265c0>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fba2926200>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │    └ <middleware.error_handling.ErrorHandlingMiddleware object at 0x71fba34af090>
          └ <middleware.rate_limiting.RateLimitingMiddleware object at 0x71fba2b17c50>
  File "/usr/local/lib/python3.11/site-packages/starlette/middleware/base.py", line 178, in __call__
    response = await self.dispatch_func(request, call_next)
                     │    │             │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x71fba2925da0>
                     │    │             └ <starlette.middleware.base._CachedRequest object at 0x71fba296f290>
                     │    └ <bound method ErrorHandlingMiddleware.dispatch of <middleware.error_handling.ErrorHandlingMiddleware object at 0x71fba34af090>>
                     └ <middleware.error_handling.ErrorHandlingMiddleware object at 0x71fba34af090>

> File "/app/API/middleware/error_handling.py", line 31, in dispatch
    response = await call_next(request)
                     │         └ <starlette.middleware.base._CachedRequest object at 0x71fba296f290>
                     └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x71fba2925da0>

  File "/usr/local/lib/python3.11/site-packages/starlette/middleware/base.py", line 156, in call_next
    raise app_exc
          └ ScrapingError("Failed to scrape story information: Scraping service initialization failed: BrowserType.launch: Executable doe...
  File "/usr/local/lib/python3.11/site-packages/starlette/middleware/base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x71fba2925f80>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fba2925b20>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │    └ <starlette.middleware.trustedhost.TrustedHostMiddleware object at 0x71fba32e8e50>
          └ <middleware.error_handling.ErrorHandlingMiddleware object at 0x71fba34af090>
  File "/usr/local/lib/python3.11/site-packages/starlette/middleware/trustedhost.py", line 51, in __call__
    await self.app(scope, receive, send)
          │    │   │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x71fba2925f80>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fba2925b20>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │    └ <starlette.middleware.cors.CORSMiddleware object at 0x71fba30cf910>
          └ <starlette.middleware.trustedhost.TrustedHostMiddleware object at 0x71fba32e8e50>
  File "/usr/local/lib/python3.11/site-packages/starlette/middleware/cors.py", line 93, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
          │    │               │      │        │                     └ Headers({'host': 'localhost:8000', 'connection': 'keep-alive', 'content-length': '160', 'sec-ch-ua-platform': '"Windows"', 'u...
          │    │               │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x71fba2925f80>
          │    │               │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fba2925b20>
          │    │               └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │    └ <function CORSMiddleware.simple_response at 0x71fba6552de0>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x71fba30cf910>
  File "/usr/local/lib/python3.11/site-packages/starlette/middleware/cors.py", line 144, in simple_response
    await self.app(scope, receive, send)
          │    │   │      │        └ functools.partial(<bound method CORSMiddleware.send of <starlette.middleware.cors.CORSMiddleware object at 0x71fba30cf910>>, ...
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fba2925b20>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x71fba34a0990>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x71fba30cf910>
  File "/usr/local/lib/python3.11/site-packages/starlette/middleware/exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ functools.partial(<bound method CORSMiddleware.send of <starlette.middleware.cors.CORSMiddleware object at 0x71fba30cf910>>, ...
          │                            │    │    │     │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fba2925b20>
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │                            │    │    └ <starlette.requests.Request object at 0x71fba296e910>
          │                            │    └ <fastapi.routing.APIRouter object at 0x71fba2ad7950>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x71fba34a0990>
          └ <function wrap_app_handling_exceptions at 0x71fba64e1f80>
  File "/usr/local/lib/python3.11/site-packages/starlette/_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "/usr/local/lib/python3.11/site-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x71fba2925a80>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fba2925b20>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          └ <fastapi.routing.APIRouter object at 0x71fba2ad7950>
  File "/usr/local/lib/python3.11/site-packages/starlette/routing.py", line 714, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x71fba2925a80>
          │    │                │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fba2925b20>
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x71fba2ad7950>>
          └ <fastapi.routing.APIRouter object at 0x71fba2ad7950>
  File "/usr/local/lib/python3.11/site-packages/starlette/routing.py", line 734, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x71fba2925a80>
          │     │      │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fba2925b20>
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │     └ <function Route.handle at 0x71fba64e3560>
          └ APIRoute(path='/api/v1/scraping/story-info', name='scrape_story_info', methods=['POST'])
  File "/usr/local/lib/python3.11/site-packages/starlette/routing.py", line 288, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x71fba2925a80>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fba2925b20>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │    └ <function request_response.<locals>.app at 0x71fba2ae8180>
          └ APIRoute(path='/api/v1/scraping/story-info', name='scrape_story_info', methods=['POST'])
  File "/usr/local/lib/python3.11/site-packages/starlette/routing.py", line 76, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
          │                            │    │        │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x71fba2925a80>
          │                            │    │        │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fba2925b20>
          │                            │    │        └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │                            │    └ <starlette.requests.Request object at 0x71fba296c050>
          │                            └ <function request_response.<locals>.app.<locals>.app at 0x71fba2924220>
          └ <function wrap_app_handling_exceptions at 0x71fba64e1f80>
  File "/usr/local/lib/python3.11/site-packages/starlette/_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "/usr/local/lib/python3.11/site-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x71fba2925c60>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fba2925b20>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          └ <function request_response.<locals>.app.<locals>.app at 0x71fba2924220>
  File "/usr/local/lib/python3.11/site-packages/starlette/routing.py", line 73, in app
    response = await f(request)
                     │ └ <starlette.requests.Request object at 0x71fba296c050>
                     └ <function get_request_handler.<locals>.app at 0x71fba2ae80e0>
  File "/usr/local/lib/python3.11/site-packages/fastapi/routing.py", line 301, in app
    raw_response = await run_endpoint_function(
                         └ <function run_endpoint_function at 0x71fba7ecb600>
  File "/usr/local/lib/python3.11/site-packages/fastapi/routing.py", line 212, in run_endpoint_function
    return await dependant.call(**values)
                 │         │      └ {'request': StoryInfoRequest(story_url=HttpUrl('https://webtruyen.diendantruyen.com/truyen/vo-dao-truong-sinh-ta-tu-hanh-co-k...
                 │         └ <function scrape_story_info at 0x71fba317eb60>
                 └ Dependant(path_params=[], query_params=[], header_params=[], cookie_params=[], body_params=[ModelField(field_info=Body(Pydant...

  File "/app/API/routers/scraping.py", line 459, in scrape_story_info
    return await controller.scrape_story_info(request, background_tasks)
                 │          │                 │        └ <fastapi.background.BackgroundTasks object at 0x71fba2952fd0>
                 │          │                 └ StoryInfoRequest(story_url=HttpUrl('https://webtruyen.diendantruyen.com/truyen/vo-dao-truong-sinh-ta-tu-hanh-co-kinh-nghiem')...
                 │          └ <function ScrapingController.scrape_story_info at 0x71fba4f02480>
                 └ <routers.scraping.ScrapingController object at 0x71fba32e89d0>

  File "/app/API/routers/scraping.py", line 108, in scrape_story_info
    raise ScrapingError(f"Failed to scrape story information: {e}")
          └ <class 'middleware.error_handling.ScrapingError'>

middleware.error_handling.ScrapingError: Failed to scrape story information: Scraping service initialization failed: BrowserType.launch: Executable doesn't exist at /home/<USER>/.cache/ms-playwright/chromium_headless_shell-1179/chrome-linux/headless_shell
╔════════════════════════════════════════════════════════════╗
║ Looks like Playwright was just installed or updated.       ║
║ Please run the following command to download new browsers: ║
║                                                            ║
║     playwright install                                     ║
║                                                            ║
║ <3 Playwright Team                                         ║
╚════════════════════════════════════════════════════════════╝
2025-07-09 08:48:57 | ERROR    | src.scraper_engine:start:125 | Failed to initialize scraper engine: BrowserType.launch: Executable doesn't exist at /home/<USER>/.cache/ms-playwright/chromium_headless_shell-1179/chrome-linux/headless_shell
╔════════════════════════════════════════════════════════════╗
║ Looks like Playwright was just installed or updated.       ║
║ Please run the following command to download new browsers: ║
║                                                            ║
║     playwright install                                     ║
║                                                            ║
║ <3 Playwright Team                                         ║
╚════════════════════════════════════════════════════════════╝

2025-07-09 08:48:57 | ERROR    | logging:callHandlers:1706 | Failed to initialize scraping service: BrowserType.launch: Executable doesn't exist at /home/<USER>/.cache/ms-playwright/chromium_headless_shell-1179/chrome-linux/headless_shell
╔════════════════════════════════════════════════════════════╗
║ Looks like Playwright was just installed or updated.       ║
║ Please run the following command to download new browsers: ║
║                                                            ║
║     playwright install                                     ║
║                                                            ║
║ <3 Playwright Team                                         ║
╚════════════════════════════════════════════════════════════╝
Traceback (most recent call last):

  File "/app/API/main.py", line 288, in <module>
    uvicorn.run(
    │       └ <function run at 0x71fba308dda0>
    └ <module 'uvicorn' from '/usr/local/lib/python3.11/site-packages/uvicorn/__init__.py'>

  File "/usr/local/lib/python3.11/site-packages/uvicorn/main.py", line 580, in run
    server.run()
    │      └ <function Server.run at 0x71fba308df80>
    └ <uvicorn.server.Server object at 0x71fba4f63d90>
  File "/usr/local/lib/python3.11/site-packages/uvicorn/server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x71fba308e020>
           │       │   └ <uvicorn.server.Server object at 0x71fba4f63d90>
           │       └ <function run at 0x71fba7acd620>
           └ <module 'asyncio' from '/usr/local/lib/python3.11/asyncio/__init__.py'>
  File "/usr/local/lib/python3.11/asyncio/runners.py", line 190, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x71fba30773d0>
           │      └ <function Runner.run at 0x71fba73d0400>
           └ <asyncio.runners.Runner object at 0x71fba32e9910>
  File "/usr/local/lib/python3.11/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at /usr/local/lib/python3.11/site-packages/uvicorn/server.py:71> wai...
           │    │     └ <cyfunction Loop.run_until_complete at 0x71fba305fe00>
           │    └ <uvloop.Loop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x71fba32e9910>
  File "/usr/local/lib/python3.11/site-packages/starlette/middleware/base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x71fb923f0220>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fb923f00e0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │    └ <starlette.middleware.trustedhost.TrustedHostMiddleware object at 0x71fba32e8e50>
          └ <middleware.error_handling.ErrorHandlingMiddleware object at 0x71fba34af090>
  File "/usr/local/lib/python3.11/site-packages/starlette/middleware/trustedhost.py", line 51, in __call__
    await self.app(scope, receive, send)
          │    │   │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x71fb923f0220>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fb923f00e0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │    └ <starlette.middleware.cors.CORSMiddleware object at 0x71fba30cf910>
          └ <starlette.middleware.trustedhost.TrustedHostMiddleware object at 0x71fba32e8e50>
  File "/usr/local/lib/python3.11/site-packages/starlette/middleware/cors.py", line 85, in __call__
    await self.app(scope, receive, send)
          │    │   │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x71fb923f0220>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fb923f00e0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x71fba34a0990>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x71fba30cf910>
  File "/usr/local/lib/python3.11/site-packages/starlette/middleware/exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x71fb923f0220>
          │                            │    │    │     │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fb923f00e0>
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │                            │    │    └ <starlette.requests.Request object at 0x71fba30daf10>
          │                            │    └ <fastapi.routing.APIRouter object at 0x71fba2ad7950>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x71fba34a0990>
          └ <function wrap_app_handling_exceptions at 0x71fba64e1f80>
  File "/usr/local/lib/python3.11/site-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x71fba2a56160>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fb923f00e0>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          └ <fastapi.routing.APIRouter object at 0x71fba2ad7950>
  File "/usr/local/lib/python3.11/site-packages/starlette/routing.py", line 714, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x71fba2a56160>
          │    │                │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fb923f00e0>
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x71fba2ad7950>>
          └ <fastapi.routing.APIRouter object at 0x71fba2ad7950>
  File "/usr/local/lib/python3.11/site-packages/starlette/routing.py", line 734, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x71fba2a56160>
          │     │      │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fb923f00e0>
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │     └ <function Route.handle at 0x71fba64e3560>
          └ APIRoute(path='/api/v1/scraping/story-info', name='scrape_story_info', methods=['POST'])
  File "/usr/local/lib/python3.11/site-packages/starlette/routing.py", line 288, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x71fba2a56160>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fb923f00e0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │    └ <function request_response.<locals>.app at 0x71fba2ae8180>
          └ APIRoute(path='/api/v1/scraping/story-info', name='scrape_story_info', methods=['POST'])
  File "/usr/local/lib/python3.11/site-packages/starlette/routing.py", line 76, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
          │                            │    │        │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x71fba2a56160>
          │                            │    │        │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fb923f00e0>
          │                            │    │        └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │                            │    └ <starlette.requests.Request object at 0x71fb925a0f10>
          │                            └ <function request_response.<locals>.app.<locals>.app at 0x71fb92393f60>
          └ <function wrap_app_handling_exceptions at 0x71fba64e1f80>
  File "/usr/local/lib/python3.11/site-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x71fb923936a0>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fb923f00e0>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          └ <function request_response.<locals>.app.<locals>.app at 0x71fb92393f60>
  File "/usr/local/lib/python3.11/site-packages/starlette/routing.py", line 73, in app
    response = await f(request)
                     │ └ <starlette.requests.Request object at 0x71fb925a0f10>
                     └ <function get_request_handler.<locals>.app at 0x71fba2ae80e0>
  File "/usr/local/lib/python3.11/site-packages/fastapi/routing.py", line 301, in app
    raw_response = await run_endpoint_function(
                         └ <function run_endpoint_function at 0x71fba7ecb600>
  File "/usr/local/lib/python3.11/site-packages/fastapi/routing.py", line 212, in run_endpoint_function
    return await dependant.call(**values)
                 │         │      └ {'request': StoryInfoRequest(story_url=HttpUrl('https://webtruyen.diendantruyen.com/truyen/vo-dao-truong-sinh-ta-tu-hanh-co-k...
                 │         └ <function scrape_story_info at 0x71fba317eb60>
                 └ Dependant(path_params=[], query_params=[], header_params=[], cookie_params=[], body_params=[ModelField(field_info=Body(Pydant...

  File "/app/API/routers/scraping.py", line 459, in scrape_story_info
    return await controller.scrape_story_info(request, background_tasks)
                 │          │                 │        └ <fastapi.background.BackgroundTasks object at 0x71fb923c1d10>
                 │          │                 └ StoryInfoRequest(story_url=HttpUrl('https://webtruyen.diendantruyen.com/truyen/vo-dao-truong-sinh-ta-tu-hanh-co-kinh-nghiem')...
                 │          └ <function ScrapingController.scrape_story_info at 0x71fba4f02480>
                 └ <routers.scraping.ScrapingController object at 0x71fba32e89d0>

  File "/app/API/routers/scraping.py", line 54, in scrape_story_info
    story_data = await self.scraping_service.scrape_story_info(
                       │    │                └ <function ScrapingService.scrape_story_info at 0x71fba4f027a0>
                       │    └ <services.scraping_service.ScrapingService object at 0x71fba32eac10>
                       └ <routers.scraping.ScrapingController object at 0x71fba32e89d0>

  File "/app/API/services/scraping_service.py", line 104, in scrape_story_info
    await self._initialize()
          │    └ <function ScrapingService._initialize at 0x71fba4f02660>
          └ <services.scraping_service.ScrapingService object at 0x71fba32eac10>

> File "/app/API/services/scraping_service.py", line 62, in _initialize
    await self._scraper.start()
          │    │        └ <function MetruyenScraper.start at 0x71fb92588cc0>
          │    └ <src.metruyenscraper.MetruyenScraper object at 0x71fb925a05d0>
          └ <services.scraping_service.ScrapingService object at 0x71fba32eac10>

  File "/app/Scraper/src/metruyenscraper.py", line 63, in start
    await self.scraper_engine.start()
          │    │              └ <function ScraperEngine.start at 0x71fb92573880>
          │    └ <src.scraper_engine.ScraperEngine object at 0x71fba34ac890>
          └ <src.metruyenscraper.MetruyenScraper object at 0x71fb925a05d0>

  File "/app/Scraper/src/scraper_engine.py", line 95, in start
    self.browser = await playwright.chromium.launch(
    │    │               │          └ <property object at 0x71fb925429d0>
    │    │               └ <playwright._impl._playwright.Playwright object at 0x71fb915f8050>
    │    └ None
    └ <src.scraper_engine.ScraperEngine object at 0x71fba34ac890>

  File "/usr/local/lib/python3.11/site-packages/playwright/async_api/_generated.py", line 14438, in launch
    await self._impl_obj.launch(
          │    │         └ <function BrowserType.launch at 0x71fb9251b4c0>
          │    └ <BrowserType name=chromium executable_path=/home/<USER>/.cache/ms-playwright/chromium-1179/chrome-linux/chrome>
          └ <BrowserType name=chromium executable_path=/home/<USER>/.cache/ms-playwright/chromium-1179/chrome-linux/chrome>
  File "/usr/local/lib/python3.11/site-packages/playwright/_impl/_browser_type.py", line 98, in launch
    await self._channel.send(
          │    │        └ <function Channel.send at 0x71fb927b4180>
          │    └ <playwright._impl._connection.Channel object at 0x71fb915e6fd0>
          └ <BrowserType name=chromium executable_path=/home/<USER>/.cache/ms-playwright/chromium-1179/chrome-linux/chrome>
  File "/usr/local/lib/python3.11/site-packages/playwright/_impl/_connection.py", line 69, in send
    return await self._connection.wrap_api_call(
                 │    │           └ <function Connection.wrap_api_call at 0x71fb92624d60>
                 │    └ <playwright._impl._connection.Connection object at 0x71fb915e5990>
                 └ <playwright._impl._connection.Channel object at 0x71fb915e6fd0>
  File "/usr/local/lib/python3.11/site-packages/playwright/_impl/_connection.py", line 558, in wrap_api_call
    raise rewrite_error(error, f"{parsed_st['apiName']}: {error}") from None
          └ <function rewrite_error at 0x71fb9275c180>

playwright._impl._errors.Error: BrowserType.launch: Executable doesn't exist at /home/<USER>/.cache/ms-playwright/chromium_headless_shell-1179/chrome-linux/headless_shell
╔════════════════════════════════════════════════════════════╗
║ Looks like Playwright was just installed or updated.       ║
║ Please run the following command to download new browsers: ║
║                                                            ║
║     playwright install                                     ║
║                                                            ║
║ <3 Playwright Team                                         ║
╚════════════════════════════════════════════════════════════╝

Traceback (most recent call last):

  File "/app/API/main.py", line 288, in <module>
    uvicorn.run(
    │       └ <function run at 0x71fba308dda0>
    └ <module 'uvicorn' from '/usr/local/lib/python3.11/site-packages/uvicorn/__init__.py'>

  File "/usr/local/lib/python3.11/site-packages/uvicorn/main.py", line 580, in run
    server.run()
    │      └ <function Server.run at 0x71fba308df80>
    └ <uvicorn.server.Server object at 0x71fba4f63d90>
  File "/usr/local/lib/python3.11/site-packages/uvicorn/server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x71fba308e020>
           │       │   └ <uvicorn.server.Server object at 0x71fba4f63d90>
           │       └ <function run at 0x71fba7acd620>
           └ <module 'asyncio' from '/usr/local/lib/python3.11/asyncio/__init__.py'>
  File "/usr/local/lib/python3.11/asyncio/runners.py", line 190, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x71fba30773d0>
           │      └ <function Runner.run at 0x71fba73d0400>
           └ <asyncio.runners.Runner object at 0x71fba32e9910>
  File "/usr/local/lib/python3.11/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at /usr/local/lib/python3.11/site-packages/uvicorn/server.py:71> wai...
           │    │     └ <cyfunction Loop.run_until_complete at 0x71fba305fe00>
           │    └ <uvloop.Loop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x71fba32e9910>
  File "/usr/local/lib/python3.11/site-packages/starlette/middleware/base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x71fb923f0220>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fb923f00e0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │    └ <starlette.middleware.trustedhost.TrustedHostMiddleware object at 0x71fba32e8e50>
          └ <middleware.error_handling.ErrorHandlingMiddleware object at 0x71fba34af090>
  File "/usr/local/lib/python3.11/site-packages/starlette/middleware/trustedhost.py", line 51, in __call__
    await self.app(scope, receive, send)
          │    │   │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x71fb923f0220>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fb923f00e0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │    └ <starlette.middleware.cors.CORSMiddleware object at 0x71fba30cf910>
          └ <starlette.middleware.trustedhost.TrustedHostMiddleware object at 0x71fba32e8e50>
  File "/usr/local/lib/python3.11/site-packages/starlette/middleware/cors.py", line 85, in __call__
    await self.app(scope, receive, send)
          │    │   │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x71fb923f0220>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fb923f00e0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x71fba34a0990>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x71fba30cf910>
  File "/usr/local/lib/python3.11/site-packages/starlette/middleware/exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x71fb923f0220>
          │                            │    │    │     │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fb923f00e0>
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │                            │    │    └ <starlette.requests.Request object at 0x71fba30daf10>
          │                            │    └ <fastapi.routing.APIRouter object at 0x71fba2ad7950>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x71fba34a0990>
          └ <function wrap_app_handling_exceptions at 0x71fba64e1f80>
  File "/usr/local/lib/python3.11/site-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x71fba2a56160>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fb923f00e0>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          └ <fastapi.routing.APIRouter object at 0x71fba2ad7950>
  File "/usr/local/lib/python3.11/site-packages/starlette/routing.py", line 714, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x71fba2a56160>
          │    │                │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fb923f00e0>
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x71fba2ad7950>>
          └ <fastapi.routing.APIRouter object at 0x71fba2ad7950>
  File "/usr/local/lib/python3.11/site-packages/starlette/routing.py", line 734, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x71fba2a56160>
          │     │      │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fb923f00e0>
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │     └ <function Route.handle at 0x71fba64e3560>
          └ APIRoute(path='/api/v1/scraping/story-info', name='scrape_story_info', methods=['POST'])
  File "/usr/local/lib/python3.11/site-packages/starlette/routing.py", line 288, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x71fba2a56160>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fb923f00e0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │    └ <function request_response.<locals>.app at 0x71fba2ae8180>
          └ APIRoute(path='/api/v1/scraping/story-info', name='scrape_story_info', methods=['POST'])
  File "/usr/local/lib/python3.11/site-packages/starlette/routing.py", line 76, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
          │                            │    │        │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x71fba2a56160>
          │                            │    │        │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fb923f00e0>
          │                            │    │        └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │                            │    └ <starlette.requests.Request object at 0x71fb925a0f10>
          │                            └ <function request_response.<locals>.app.<locals>.app at 0x71fb92393f60>
          └ <function wrap_app_handling_exceptions at 0x71fba64e1f80>
  File "/usr/local/lib/python3.11/site-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x71fb923936a0>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fb923f00e0>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          └ <function request_response.<locals>.app.<locals>.app at 0x71fb92393f60>
  File "/usr/local/lib/python3.11/site-packages/starlette/routing.py", line 73, in app
    response = await f(request)
                     │ └ <starlette.requests.Request object at 0x71fb925a0f10>
                     └ <function get_request_handler.<locals>.app at 0x71fba2ae80e0>
  File "/usr/local/lib/python3.11/site-packages/fastapi/routing.py", line 301, in app
    raw_response = await run_endpoint_function(
                         └ <function run_endpoint_function at 0x71fba7ecb600>
  File "/usr/local/lib/python3.11/site-packages/fastapi/routing.py", line 212, in run_endpoint_function
    return await dependant.call(**values)
                 │         │      └ {'request': StoryInfoRequest(story_url=HttpUrl('https://webtruyen.diendantruyen.com/truyen/vo-dao-truong-sinh-ta-tu-hanh-co-k...
                 │         └ <function scrape_story_info at 0x71fba317eb60>
                 └ Dependant(path_params=[], query_params=[], header_params=[], cookie_params=[], body_params=[ModelField(field_info=Body(Pydant...

  File "/app/API/routers/scraping.py", line 459, in scrape_story_info
    return await controller.scrape_story_info(request, background_tasks)
                 │          │                 │        └ <fastapi.background.BackgroundTasks object at 0x71fb923c1d10>
                 │          │                 └ StoryInfoRequest(story_url=HttpUrl('https://webtruyen.diendantruyen.com/truyen/vo-dao-truong-sinh-ta-tu-hanh-co-kinh-nghiem')...
                 │          └ <function ScrapingController.scrape_story_info at 0x71fba4f02480>
                 └ <routers.scraping.ScrapingController object at 0x71fba32e89d0>

  File "/app/API/routers/scraping.py", line 54, in scrape_story_info
    story_data = await self.scraping_service.scrape_story_info(
                       │    │                └ <function ScrapingService.scrape_story_info at 0x71fba4f027a0>
                       │    └ <services.scraping_service.ScrapingService object at 0x71fba32eac10>
                       └ <routers.scraping.ScrapingController object at 0x71fba32e89d0>

  File "/app/API/services/scraping_service.py", line 104, in scrape_story_info
    await self._initialize()
          │    └ <function ScrapingService._initialize at 0x71fba4f02660>
          └ <services.scraping_service.ScrapingService object at 0x71fba32eac10>

> File "/app/API/services/scraping_service.py", line 62, in _initialize
    await self._scraper.start()
          │    │        └ <function MetruyenScraper.start at 0x71fb92588cc0>
          │    └ <src.metruyenscraper.MetruyenScraper object at 0x71fb925a05d0>
          └ <services.scraping_service.ScrapingService object at 0x71fba32eac10>

  File "/app/Scraper/src/metruyenscraper.py", line 63, in start
    await self.scraper_engine.start()
          │    │              └ <function ScraperEngine.start at 0x71fb92573880>
          │    └ <src.scraper_engine.ScraperEngine object at 0x71fba34ac890>
          └ <src.metruyenscraper.MetruyenScraper object at 0x71fb925a05d0>

  File "/app/Scraper/src/scraper_engine.py", line 95, in start
    self.browser = await playwright.chromium.launch(
    │    │               │          └ <property object at 0x71fb925429d0>
    │    │               └ <playwright._impl._playwright.Playwright object at 0x71fb915f8050>
    │    └ None
    └ <src.scraper_engine.ScraperEngine object at 0x71fba34ac890>

  File "/usr/local/lib/python3.11/site-packages/playwright/async_api/_generated.py", line 14438, in launch
    await self._impl_obj.launch(
          │    │         └ <function BrowserType.launch at 0x71fb9251b4c0>
          │    └ <BrowserType name=chromium executable_path=/home/<USER>/.cache/ms-playwright/chromium-1179/chrome-linux/chrome>
          └ <BrowserType name=chromium executable_path=/home/<USER>/.cache/ms-playwright/chromium-1179/chrome-linux/chrome>
  File "/usr/local/lib/python3.11/site-packages/playwright/_impl/_browser_type.py", line 98, in launch
    await self._channel.send(
          │    │        └ <function Channel.send at 0x71fb927b4180>
          │    └ <playwright._impl._connection.Channel object at 0x71fb915e6fd0>
          └ <BrowserType name=chromium executable_path=/home/<USER>/.cache/ms-playwright/chromium-1179/chrome-linux/chrome>
  File "/usr/local/lib/python3.11/site-packages/playwright/_impl/_connection.py", line 69, in send
    return await self._connection.wrap_api_call(
                 │    │           └ <function Connection.wrap_api_call at 0x71fb92624d60>
                 │    └ <playwright._impl._connection.Connection object at 0x71fb915e5990>
                 └ <playwright._impl._connection.Channel object at 0x71fb915e6fd0>
  File "/usr/local/lib/python3.11/site-packages/playwright/_impl/_connection.py", line 558, in wrap_api_call
    raise rewrite_error(error, f"{parsed_st['apiName']}: {error}") from None
          └ <function rewrite_error at 0x71fb9275c180>

playwright._impl._errors.Error: BrowserType.launch: Executable doesn't exist at /home/<USER>/.cache/ms-playwright/chromium_headless_shell-1179/chrome-linux/headless_shell
╔════════════════════════════════════════════════════════════╗
║ Looks like Playwright was just installed or updated.       ║
║ Please run the following command to download new browsers: ║
║                                                            ║
║     playwright install                                     ║
║                                                            ║
║ <3 Playwright Team                                         ║
╚════════════════════════════════════════════════════════════╝
2025-07-09 08:48:57 | ERROR    | logging:callHandlers:1706 | Exception type: Error
Traceback (most recent call last):

  File "/app/API/main.py", line 288, in <module>
    uvicorn.run(
    │       └ <function run at 0x71fba308dda0>
    └ <module 'uvicorn' from '/usr/local/lib/python3.11/site-packages/uvicorn/__init__.py'>

  File "/usr/local/lib/python3.11/site-packages/uvicorn/main.py", line 580, in run
    server.run()
    │      └ <function Server.run at 0x71fba308df80>
    └ <uvicorn.server.Server object at 0x71fba4f63d90>
  File "/usr/local/lib/python3.11/site-packages/uvicorn/server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x71fba308e020>
           │       │   └ <uvicorn.server.Server object at 0x71fba4f63d90>
           │       └ <function run at 0x71fba7acd620>
           └ <module 'asyncio' from '/usr/local/lib/python3.11/asyncio/__init__.py'>
  File "/usr/local/lib/python3.11/asyncio/runners.py", line 190, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x71fba30773d0>
           │      └ <function Runner.run at 0x71fba73d0400>
           └ <asyncio.runners.Runner object at 0x71fba32e9910>
  File "/usr/local/lib/python3.11/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at /usr/local/lib/python3.11/site-packages/uvicorn/server.py:71> wai...
           │    │     └ <cyfunction Loop.run_until_complete at 0x71fba305fe00>
           │    └ <uvloop.Loop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x71fba32e9910>
  File "/usr/local/lib/python3.11/site-packages/starlette/middleware/base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x71fb923f0220>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fb923f00e0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │    └ <starlette.middleware.trustedhost.TrustedHostMiddleware object at 0x71fba32e8e50>
          └ <middleware.error_handling.ErrorHandlingMiddleware object at 0x71fba34af090>
  File "/usr/local/lib/python3.11/site-packages/starlette/middleware/trustedhost.py", line 51, in __call__
    await self.app(scope, receive, send)
          │    │   │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x71fb923f0220>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fb923f00e0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │    └ <starlette.middleware.cors.CORSMiddleware object at 0x71fba30cf910>
          └ <starlette.middleware.trustedhost.TrustedHostMiddleware object at 0x71fba32e8e50>
  File "/usr/local/lib/python3.11/site-packages/starlette/middleware/cors.py", line 85, in __call__
    await self.app(scope, receive, send)
          │    │   │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x71fb923f0220>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fb923f00e0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x71fba34a0990>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x71fba30cf910>
  File "/usr/local/lib/python3.11/site-packages/starlette/middleware/exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x71fb923f0220>
          │                            │    │    │     │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fb923f00e0>
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │                            │    │    └ <starlette.requests.Request object at 0x71fba30daf10>
          │                            │    └ <fastapi.routing.APIRouter object at 0x71fba2ad7950>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x71fba34a0990>
          └ <function wrap_app_handling_exceptions at 0x71fba64e1f80>
  File "/usr/local/lib/python3.11/site-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x71fba2a56160>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fb923f00e0>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          └ <fastapi.routing.APIRouter object at 0x71fba2ad7950>
  File "/usr/local/lib/python3.11/site-packages/starlette/routing.py", line 714, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x71fba2a56160>
          │    │                │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fb923f00e0>
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x71fba2ad7950>>
          └ <fastapi.routing.APIRouter object at 0x71fba2ad7950>
  File "/usr/local/lib/python3.11/site-packages/starlette/routing.py", line 734, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x71fba2a56160>
          │     │      │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fb923f00e0>
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │     └ <function Route.handle at 0x71fba64e3560>
          └ APIRoute(path='/api/v1/scraping/story-info', name='scrape_story_info', methods=['POST'])
  File "/usr/local/lib/python3.11/site-packages/starlette/routing.py", line 288, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x71fba2a56160>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fb923f00e0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │    └ <function request_response.<locals>.app at 0x71fba2ae8180>
          └ APIRoute(path='/api/v1/scraping/story-info', name='scrape_story_info', methods=['POST'])
  File "/usr/local/lib/python3.11/site-packages/starlette/routing.py", line 76, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
          │                            │    │        │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x71fba2a56160>
          │                            │    │        │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fb923f00e0>
          │                            │    │        └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │                            │    └ <starlette.requests.Request object at 0x71fb925a0f10>
          │                            └ <function request_response.<locals>.app.<locals>.app at 0x71fb92393f60>
          └ <function wrap_app_handling_exceptions at 0x71fba64e1f80>
  File "/usr/local/lib/python3.11/site-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x71fb923936a0>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fb923f00e0>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          └ <function request_response.<locals>.app.<locals>.app at 0x71fb92393f60>
  File "/usr/local/lib/python3.11/site-packages/starlette/routing.py", line 73, in app
    response = await f(request)
                     │ └ <starlette.requests.Request object at 0x71fb925a0f10>
                     └ <function get_request_handler.<locals>.app at 0x71fba2ae80e0>
  File "/usr/local/lib/python3.11/site-packages/fastapi/routing.py", line 301, in app
    raw_response = await run_endpoint_function(
                         └ <function run_endpoint_function at 0x71fba7ecb600>
  File "/usr/local/lib/python3.11/site-packages/fastapi/routing.py", line 212, in run_endpoint_function
    return await dependant.call(**values)
                 │         │      └ {'request': StoryInfoRequest(story_url=HttpUrl('https://webtruyen.diendantruyen.com/truyen/vo-dao-truong-sinh-ta-tu-hanh-co-k...
                 │         └ <function scrape_story_info at 0x71fba317eb60>
                 └ Dependant(path_params=[], query_params=[], header_params=[], cookie_params=[], body_params=[ModelField(field_info=Body(Pydant...

  File "/app/API/routers/scraping.py", line 459, in scrape_story_info
    return await controller.scrape_story_info(request, background_tasks)
                 │          │                 │        └ <fastapi.background.BackgroundTasks object at 0x71fb923c1d10>
                 │          │                 └ StoryInfoRequest(story_url=HttpUrl('https://webtruyen.diendantruyen.com/truyen/vo-dao-truong-sinh-ta-tu-hanh-co-kinh-nghiem')...
                 │          └ <function ScrapingController.scrape_story_info at 0x71fba4f02480>
                 └ <routers.scraping.ScrapingController object at 0x71fba32e89d0>

  File "/app/API/routers/scraping.py", line 54, in scrape_story_info
    story_data = await self.scraping_service.scrape_story_info(
                       │    │                └ <function ScrapingService.scrape_story_info at 0x71fba4f027a0>
                       │    └ <services.scraping_service.ScrapingService object at 0x71fba32eac10>
                       └ <routers.scraping.ScrapingController object at 0x71fba32e89d0>

  File "/app/API/services/scraping_service.py", line 104, in scrape_story_info
    await self._initialize()
          │    └ <function ScrapingService._initialize at 0x71fba4f02660>
          └ <services.scraping_service.ScrapingService object at 0x71fba32eac10>

> File "/app/API/services/scraping_service.py", line 62, in _initialize
    await self._scraper.start()
          │    │        └ <function MetruyenScraper.start at 0x71fb92588cc0>
          │    └ <src.metruyenscraper.MetruyenScraper object at 0x71fb925a05d0>
          └ <services.scraping_service.ScrapingService object at 0x71fba32eac10>

  File "/app/Scraper/src/metruyenscraper.py", line 63, in start
    await self.scraper_engine.start()
          │    │              └ <function ScraperEngine.start at 0x71fb92573880>
          │    └ <src.scraper_engine.ScraperEngine object at 0x71fba34ac890>
          └ <src.metruyenscraper.MetruyenScraper object at 0x71fb925a05d0>

  File "/app/Scraper/src/scraper_engine.py", line 95, in start
    self.browser = await playwright.chromium.launch(
    │    │               │          └ <property object at 0x71fb925429d0>
    │    │               └ <playwright._impl._playwright.Playwright object at 0x71fb915f8050>
    │    └ None
    └ <src.scraper_engine.ScraperEngine object at 0x71fba34ac890>

  File "/usr/local/lib/python3.11/site-packages/playwright/async_api/_generated.py", line 14438, in launch
    await self._impl_obj.launch(
          │    │         └ <function BrowserType.launch at 0x71fb9251b4c0>
          │    └ <BrowserType name=chromium executable_path=/home/<USER>/.cache/ms-playwright/chromium-1179/chrome-linux/chrome>
          └ <BrowserType name=chromium executable_path=/home/<USER>/.cache/ms-playwright/chromium-1179/chrome-linux/chrome>
  File "/usr/local/lib/python3.11/site-packages/playwright/_impl/_browser_type.py", line 98, in launch
    await self._channel.send(
          │    │        └ <function Channel.send at 0x71fb927b4180>
          │    └ <playwright._impl._connection.Channel object at 0x71fb915e6fd0>
          └ <BrowserType name=chromium executable_path=/home/<USER>/.cache/ms-playwright/chromium-1179/chrome-linux/chrome>
  File "/usr/local/lib/python3.11/site-packages/playwright/_impl/_connection.py", line 69, in send
    return await self._connection.wrap_api_call(
                 │    │           └ <function Connection.wrap_api_call at 0x71fb92624d60>
                 │    └ <playwright._impl._connection.Connection object at 0x71fb915e5990>
                 └ <playwright._impl._connection.Channel object at 0x71fb915e6fd0>
  File "/usr/local/lib/python3.11/site-packages/playwright/_impl/_connection.py", line 558, in wrap_api_call
    raise rewrite_error(error, f"{parsed_st['apiName']}: {error}") from None
          └ <function rewrite_error at 0x71fb9275c180>

playwright._impl._errors.Error: BrowserType.launch: Executable doesn't exist at /home/<USER>/.cache/ms-playwright/chromium_headless_shell-1179/chrome-linux/headless_shell
╔════════════════════════════════════════════════════════════╗
║ Looks like Playwright was just installed or updated.       ║
║ Please run the following command to download new browsers: ║
║                                                            ║
║     playwright install                                     ║
║                                                            ║
║ <3 Playwright Team                                         ║
╚════════════════════════════════════════════════════════════╝

Traceback (most recent call last):

  File "/app/API/main.py", line 288, in <module>
    uvicorn.run(
    │       └ <function run at 0x71fba308dda0>
    └ <module 'uvicorn' from '/usr/local/lib/python3.11/site-packages/uvicorn/__init__.py'>

  File "/usr/local/lib/python3.11/site-packages/uvicorn/main.py", line 580, in run
    server.run()
    │      └ <function Server.run at 0x71fba308df80>
    └ <uvicorn.server.Server object at 0x71fba4f63d90>
  File "/usr/local/lib/python3.11/site-packages/uvicorn/server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x71fba308e020>
           │       │   └ <uvicorn.server.Server object at 0x71fba4f63d90>
           │       └ <function run at 0x71fba7acd620>
           └ <module 'asyncio' from '/usr/local/lib/python3.11/asyncio/__init__.py'>
  File "/usr/local/lib/python3.11/asyncio/runners.py", line 190, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x71fba30773d0>
           │      └ <function Runner.run at 0x71fba73d0400>
           └ <asyncio.runners.Runner object at 0x71fba32e9910>
  File "/usr/local/lib/python3.11/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at /usr/local/lib/python3.11/site-packages/uvicorn/server.py:71> wai...
           │    │     └ <cyfunction Loop.run_until_complete at 0x71fba305fe00>
           │    └ <uvloop.Loop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x71fba32e9910>
  File "/usr/local/lib/python3.11/site-packages/starlette/middleware/base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x71fb923f0220>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fb923f00e0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │    └ <starlette.middleware.trustedhost.TrustedHostMiddleware object at 0x71fba32e8e50>
          └ <middleware.error_handling.ErrorHandlingMiddleware object at 0x71fba34af090>
  File "/usr/local/lib/python3.11/site-packages/starlette/middleware/trustedhost.py", line 51, in __call__
    await self.app(scope, receive, send)
          │    │   │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x71fb923f0220>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fb923f00e0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │    └ <starlette.middleware.cors.CORSMiddleware object at 0x71fba30cf910>
          └ <starlette.middleware.trustedhost.TrustedHostMiddleware object at 0x71fba32e8e50>
  File "/usr/local/lib/python3.11/site-packages/starlette/middleware/cors.py", line 85, in __call__
    await self.app(scope, receive, send)
          │    │   │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x71fb923f0220>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fb923f00e0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x71fba34a0990>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x71fba30cf910>
  File "/usr/local/lib/python3.11/site-packages/starlette/middleware/exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x71fb923f0220>
          │                            │    │    │     │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fb923f00e0>
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │                            │    │    └ <starlette.requests.Request object at 0x71fba30daf10>
          │                            │    └ <fastapi.routing.APIRouter object at 0x71fba2ad7950>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x71fba34a0990>
          └ <function wrap_app_handling_exceptions at 0x71fba64e1f80>
  File "/usr/local/lib/python3.11/site-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x71fba2a56160>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fb923f00e0>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          └ <fastapi.routing.APIRouter object at 0x71fba2ad7950>
  File "/usr/local/lib/python3.11/site-packages/starlette/routing.py", line 714, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x71fba2a56160>
          │    │                │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fb923f00e0>
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x71fba2ad7950>>
          └ <fastapi.routing.APIRouter object at 0x71fba2ad7950>
  File "/usr/local/lib/python3.11/site-packages/starlette/routing.py", line 734, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x71fba2a56160>
          │     │      │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fb923f00e0>
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │     └ <function Route.handle at 0x71fba64e3560>
          └ APIRoute(path='/api/v1/scraping/story-info', name='scrape_story_info', methods=['POST'])
  File "/usr/local/lib/python3.11/site-packages/starlette/routing.py", line 288, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x71fba2a56160>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fb923f00e0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │    └ <function request_response.<locals>.app at 0x71fba2ae8180>
          └ APIRoute(path='/api/v1/scraping/story-info', name='scrape_story_info', methods=['POST'])
  File "/usr/local/lib/python3.11/site-packages/starlette/routing.py", line 76, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
          │                            │    │        │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x71fba2a56160>
          │                            │    │        │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fb923f00e0>
          │                            │    │        └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │                            │    └ <starlette.requests.Request object at 0x71fb925a0f10>
          │                            └ <function request_response.<locals>.app.<locals>.app at 0x71fb92393f60>
          └ <function wrap_app_handling_exceptions at 0x71fba64e1f80>
  File "/usr/local/lib/python3.11/site-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x71fb923936a0>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fb923f00e0>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          └ <function request_response.<locals>.app.<locals>.app at 0x71fb92393f60>
  File "/usr/local/lib/python3.11/site-packages/starlette/routing.py", line 73, in app
    response = await f(request)
                     │ └ <starlette.requests.Request object at 0x71fb925a0f10>
                     └ <function get_request_handler.<locals>.app at 0x71fba2ae80e0>
  File "/usr/local/lib/python3.11/site-packages/fastapi/routing.py", line 301, in app
    raw_response = await run_endpoint_function(
                         └ <function run_endpoint_function at 0x71fba7ecb600>
  File "/usr/local/lib/python3.11/site-packages/fastapi/routing.py", line 212, in run_endpoint_function
    return await dependant.call(**values)
                 │         │      └ {'request': StoryInfoRequest(story_url=HttpUrl('https://webtruyen.diendantruyen.com/truyen/vo-dao-truong-sinh-ta-tu-hanh-co-k...
                 │         └ <function scrape_story_info at 0x71fba317eb60>
                 └ Dependant(path_params=[], query_params=[], header_params=[], cookie_params=[], body_params=[ModelField(field_info=Body(Pydant...

  File "/app/API/routers/scraping.py", line 459, in scrape_story_info
    return await controller.scrape_story_info(request, background_tasks)
                 │          │                 │        └ <fastapi.background.BackgroundTasks object at 0x71fb923c1d10>
                 │          │                 └ StoryInfoRequest(story_url=HttpUrl('https://webtruyen.diendantruyen.com/truyen/vo-dao-truong-sinh-ta-tu-hanh-co-kinh-nghiem')...
                 │          └ <function ScrapingController.scrape_story_info at 0x71fba4f02480>
                 └ <routers.scraping.ScrapingController object at 0x71fba32e89d0>

  File "/app/API/routers/scraping.py", line 54, in scrape_story_info
    story_data = await self.scraping_service.scrape_story_info(
                       │    │                └ <function ScrapingService.scrape_story_info at 0x71fba4f027a0>
                       │    └ <services.scraping_service.ScrapingService object at 0x71fba32eac10>
                       └ <routers.scraping.ScrapingController object at 0x71fba32e89d0>

  File "/app/API/services/scraping_service.py", line 104, in scrape_story_info
    await self._initialize()
          │    └ <function ScrapingService._initialize at 0x71fba4f02660>
          └ <services.scraping_service.ScrapingService object at 0x71fba32eac10>

> File "/app/API/services/scraping_service.py", line 62, in _initialize
    await self._scraper.start()
          │    │        └ <function MetruyenScraper.start at 0x71fb92588cc0>
          │    └ <src.metruyenscraper.MetruyenScraper object at 0x71fb925a05d0>
          └ <services.scraping_service.ScrapingService object at 0x71fba32eac10>

  File "/app/Scraper/src/metruyenscraper.py", line 63, in start
    await self.scraper_engine.start()
          │    │              └ <function ScraperEngine.start at 0x71fb92573880>
          │    └ <src.scraper_engine.ScraperEngine object at 0x71fba34ac890>
          └ <src.metruyenscraper.MetruyenScraper object at 0x71fb925a05d0>

  File "/app/Scraper/src/scraper_engine.py", line 95, in start
    self.browser = await playwright.chromium.launch(
    │    │               │          └ <property object at 0x71fb925429d0>
    │    │               └ <playwright._impl._playwright.Playwright object at 0x71fb915f8050>
    │    └ None
    └ <src.scraper_engine.ScraperEngine object at 0x71fba34ac890>

  File "/usr/local/lib/python3.11/site-packages/playwright/async_api/_generated.py", line 14438, in launch
    await self._impl_obj.launch(
          │    │         └ <function BrowserType.launch at 0x71fb9251b4c0>
          │    └ <BrowserType name=chromium executable_path=/home/<USER>/.cache/ms-playwright/chromium-1179/chrome-linux/chrome>
          └ <BrowserType name=chromium executable_path=/home/<USER>/.cache/ms-playwright/chromium-1179/chrome-linux/chrome>
  File "/usr/local/lib/python3.11/site-packages/playwright/_impl/_browser_type.py", line 98, in launch
    await self._channel.send(
          │    │        └ <function Channel.send at 0x71fb927b4180>
          │    └ <playwright._impl._connection.Channel object at 0x71fb915e6fd0>
          └ <BrowserType name=chromium executable_path=/home/<USER>/.cache/ms-playwright/chromium-1179/chrome-linux/chrome>
  File "/usr/local/lib/python3.11/site-packages/playwright/_impl/_connection.py", line 69, in send
    return await self._connection.wrap_api_call(
                 │    │           └ <function Connection.wrap_api_call at 0x71fb92624d60>
                 │    └ <playwright._impl._connection.Connection object at 0x71fb915e5990>
                 └ <playwright._impl._connection.Channel object at 0x71fb915e6fd0>
  File "/usr/local/lib/python3.11/site-packages/playwright/_impl/_connection.py", line 558, in wrap_api_call
    raise rewrite_error(error, f"{parsed_st['apiName']}: {error}") from None
          └ <function rewrite_error at 0x71fb9275c180>

playwright._impl._errors.Error: BrowserType.launch: Executable doesn't exist at /home/<USER>/.cache/ms-playwright/chromium_headless_shell-1179/chrome-linux/headless_shell
╔════════════════════════════════════════════════════════════╗
║ Looks like Playwright was just installed or updated.       ║
║ Please run the following command to download new browsers: ║
║                                                            ║
║     playwright install                                     ║
║                                                            ║
║ <3 Playwright Team                                         ║
╚════════════════════════════════════════════════════════════╝
2025-07-09 08:48:57 | ERROR    | logging:callHandlers:1706 | Traceback: Traceback (most recent call last):
  File "/app/API/services/scraping_service.py", line 62, in _initialize
    await self._scraper.start()
  File "/app/Scraper/src/metruyenscraper.py", line 63, in start
    await self.scraper_engine.start()
  File "/app/Scraper/src/scraper_engine.py", line 95, in start
    self.browser = await playwright.chromium.launch(
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/playwright/async_api/_generated.py", line 14438, in launch
    await self._impl_obj.launch(
  File "/usr/local/lib/python3.11/site-packages/playwright/_impl/_browser_type.py", line 98, in launch
    await self._channel.send(
  File "/usr/local/lib/python3.11/site-packages/playwright/_impl/_connection.py", line 69, in send
    return await self._connection.wrap_api_call(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/playwright/_impl/_connection.py", line 558, in wrap_api_call
    raise rewrite_error(error, f"{parsed_st['apiName']}: {error}") from None
playwright._impl._errors.Error: BrowserType.launch: Executable doesn't exist at /home/<USER>/.cache/ms-playwright/chromium_headless_shell-1179/chrome-linux/headless_shell
╔════════════════════════════════════════════════════════════╗
║ Looks like Playwright was just installed or updated.       ║
║ Please run the following command to download new browsers: ║
║                                                            ║
║     playwright install                                     ║
║                                                            ║
║ <3 Playwright Team                                         ║
╚════════════════════════════════════════════════════════════╝

Traceback (most recent call last):

  File "/app/API/main.py", line 288, in <module>
    uvicorn.run(
    │       └ <function run at 0x71fba308dda0>
    └ <module 'uvicorn' from '/usr/local/lib/python3.11/site-packages/uvicorn/__init__.py'>

  File "/usr/local/lib/python3.11/site-packages/uvicorn/main.py", line 580, in run
    server.run()
    │      └ <function Server.run at 0x71fba308df80>
    └ <uvicorn.server.Server object at 0x71fba4f63d90>
  File "/usr/local/lib/python3.11/site-packages/uvicorn/server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x71fba308e020>
           │       │   └ <uvicorn.server.Server object at 0x71fba4f63d90>
           │       └ <function run at 0x71fba7acd620>
           └ <module 'asyncio' from '/usr/local/lib/python3.11/asyncio/__init__.py'>
  File "/usr/local/lib/python3.11/asyncio/runners.py", line 190, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x71fba30773d0>
           │      └ <function Runner.run at 0x71fba73d0400>
           └ <asyncio.runners.Runner object at 0x71fba32e9910>
  File "/usr/local/lib/python3.11/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at /usr/local/lib/python3.11/site-packages/uvicorn/server.py:71> wai...
           │    │     └ <cyfunction Loop.run_until_complete at 0x71fba305fe00>
           │    └ <uvloop.Loop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x71fba32e9910>
  File "/usr/local/lib/python3.11/site-packages/starlette/middleware/base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x71fb923f0220>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fb923f00e0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │    └ <starlette.middleware.trustedhost.TrustedHostMiddleware object at 0x71fba32e8e50>
          └ <middleware.error_handling.ErrorHandlingMiddleware object at 0x71fba34af090>
  File "/usr/local/lib/python3.11/site-packages/starlette/middleware/trustedhost.py", line 51, in __call__
    await self.app(scope, receive, send)
          │    │   │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x71fb923f0220>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fb923f00e0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │    └ <starlette.middleware.cors.CORSMiddleware object at 0x71fba30cf910>
          └ <starlette.middleware.trustedhost.TrustedHostMiddleware object at 0x71fba32e8e50>
  File "/usr/local/lib/python3.11/site-packages/starlette/middleware/cors.py", line 85, in __call__
    await self.app(scope, receive, send)
          │    │   │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x71fb923f0220>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fb923f00e0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x71fba34a0990>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x71fba30cf910>
  File "/usr/local/lib/python3.11/site-packages/starlette/middleware/exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x71fb923f0220>
          │                            │    │    │     │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fb923f00e0>
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │                            │    │    └ <starlette.requests.Request object at 0x71fba30daf10>
          │                            │    └ <fastapi.routing.APIRouter object at 0x71fba2ad7950>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x71fba34a0990>
          └ <function wrap_app_handling_exceptions at 0x71fba64e1f80>
  File "/usr/local/lib/python3.11/site-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x71fba2a56160>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fb923f00e0>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          └ <fastapi.routing.APIRouter object at 0x71fba2ad7950>
  File "/usr/local/lib/python3.11/site-packages/starlette/routing.py", line 714, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x71fba2a56160>
          │    │                │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fb923f00e0>
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x71fba2ad7950>>
          └ <fastapi.routing.APIRouter object at 0x71fba2ad7950>
  File "/usr/local/lib/python3.11/site-packages/starlette/routing.py", line 734, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x71fba2a56160>
          │     │      │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fb923f00e0>
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │     └ <function Route.handle at 0x71fba64e3560>
          └ APIRoute(path='/api/v1/scraping/story-info', name='scrape_story_info', methods=['POST'])
  File "/usr/local/lib/python3.11/site-packages/starlette/routing.py", line 288, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x71fba2a56160>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fb923f00e0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │    └ <function request_response.<locals>.app at 0x71fba2ae8180>
          └ APIRoute(path='/api/v1/scraping/story-info', name='scrape_story_info', methods=['POST'])
  File "/usr/local/lib/python3.11/site-packages/starlette/routing.py", line 76, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
          │                            │    │        │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x71fba2a56160>
          │                            │    │        │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fb923f00e0>
          │                            │    │        └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │                            │    └ <starlette.requests.Request object at 0x71fb925a0f10>
          │                            └ <function request_response.<locals>.app.<locals>.app at 0x71fb92393f60>
          └ <function wrap_app_handling_exceptions at 0x71fba64e1f80>
  File "/usr/local/lib/python3.11/site-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x71fb923936a0>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fb923f00e0>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          └ <function request_response.<locals>.app.<locals>.app at 0x71fb92393f60>
  File "/usr/local/lib/python3.11/site-packages/starlette/routing.py", line 73, in app
    response = await f(request)
                     │ └ <starlette.requests.Request object at 0x71fb925a0f10>
                     └ <function get_request_handler.<locals>.app at 0x71fba2ae80e0>
  File "/usr/local/lib/python3.11/site-packages/fastapi/routing.py", line 301, in app
    raw_response = await run_endpoint_function(
                         └ <function run_endpoint_function at 0x71fba7ecb600>
  File "/usr/local/lib/python3.11/site-packages/fastapi/routing.py", line 212, in run_endpoint_function
    return await dependant.call(**values)
                 │         │      └ {'request': StoryInfoRequest(story_url=HttpUrl('https://webtruyen.diendantruyen.com/truyen/vo-dao-truong-sinh-ta-tu-hanh-co-k...
                 │         └ <function scrape_story_info at 0x71fba317eb60>
                 └ Dependant(path_params=[], query_params=[], header_params=[], cookie_params=[], body_params=[ModelField(field_info=Body(Pydant...

  File "/app/API/routers/scraping.py", line 459, in scrape_story_info
    return await controller.scrape_story_info(request, background_tasks)
                 │          │                 │        └ <fastapi.background.BackgroundTasks object at 0x71fb923c1d10>
                 │          │                 └ StoryInfoRequest(story_url=HttpUrl('https://webtruyen.diendantruyen.com/truyen/vo-dao-truong-sinh-ta-tu-hanh-co-kinh-nghiem')...
                 │          └ <function ScrapingController.scrape_story_info at 0x71fba4f02480>
                 └ <routers.scraping.ScrapingController object at 0x71fba32e89d0>

  File "/app/API/routers/scraping.py", line 54, in scrape_story_info
    story_data = await self.scraping_service.scrape_story_info(
                       │    │                └ <function ScrapingService.scrape_story_info at 0x71fba4f027a0>
                       │    └ <services.scraping_service.ScrapingService object at 0x71fba32eac10>
                       └ <routers.scraping.ScrapingController object at 0x71fba32e89d0>

  File "/app/API/services/scraping_service.py", line 104, in scrape_story_info
    await self._initialize()
          │    └ <function ScrapingService._initialize at 0x71fba4f02660>
          └ <services.scraping_service.ScrapingService object at 0x71fba32eac10>

> File "/app/API/services/scraping_service.py", line 62, in _initialize
    await self._scraper.start()
          │    │        └ <function MetruyenScraper.start at 0x71fb92588cc0>
          │    └ <src.metruyenscraper.MetruyenScraper object at 0x71fb925a05d0>
          └ <services.scraping_service.ScrapingService object at 0x71fba32eac10>

  File "/app/Scraper/src/metruyenscraper.py", line 63, in start
    await self.scraper_engine.start()
          │    │              └ <function ScraperEngine.start at 0x71fb92573880>
          │    └ <src.scraper_engine.ScraperEngine object at 0x71fba34ac890>
          └ <src.metruyenscraper.MetruyenScraper object at 0x71fb925a05d0>

  File "/app/Scraper/src/scraper_engine.py", line 95, in start
    self.browser = await playwright.chromium.launch(
    │    │               │          └ <property object at 0x71fb925429d0>
    │    │               └ <playwright._impl._playwright.Playwright object at 0x71fb915f8050>
    │    └ None
    └ <src.scraper_engine.ScraperEngine object at 0x71fba34ac890>

  File "/usr/local/lib/python3.11/site-packages/playwright/async_api/_generated.py", line 14438, in launch
    await self._impl_obj.launch(
          │    │         └ <function BrowserType.launch at 0x71fb9251b4c0>
          │    └ <BrowserType name=chromium executable_path=/home/<USER>/.cache/ms-playwright/chromium-1179/chrome-linux/chrome>
          └ <BrowserType name=chromium executable_path=/home/<USER>/.cache/ms-playwright/chromium-1179/chrome-linux/chrome>
  File "/usr/local/lib/python3.11/site-packages/playwright/_impl/_browser_type.py", line 98, in launch
    await self._channel.send(
          │    │        └ <function Channel.send at 0x71fb927b4180>
          │    └ <playwright._impl._connection.Channel object at 0x71fb915e6fd0>
          └ <BrowserType name=chromium executable_path=/home/<USER>/.cache/ms-playwright/chromium-1179/chrome-linux/chrome>
  File "/usr/local/lib/python3.11/site-packages/playwright/_impl/_connection.py", line 69, in send
    return await self._connection.wrap_api_call(
                 │    │           └ <function Connection.wrap_api_call at 0x71fb92624d60>
                 │    └ <playwright._impl._connection.Connection object at 0x71fb915e5990>
                 └ <playwright._impl._connection.Channel object at 0x71fb915e6fd0>
  File "/usr/local/lib/python3.11/site-packages/playwright/_impl/_connection.py", line 558, in wrap_api_call
    raise rewrite_error(error, f"{parsed_st['apiName']}: {error}") from None
          └ <function rewrite_error at 0x71fb9275c180>

playwright._impl._errors.Error: BrowserType.launch: Executable doesn't exist at /home/<USER>/.cache/ms-playwright/chromium_headless_shell-1179/chrome-linux/headless_shell
╔════════════════════════════════════════════════════════════╗
║ Looks like Playwright was just installed or updated.       ║
║ Please run the following command to download new browsers: ║
║                                                            ║
║     playwright install                                     ║
║                                                            ║
║ <3 Playwright Team                                         ║
╚════════════════════════════════════════════════════════════╝

Traceback (most recent call last):

  File "/app/API/main.py", line 288, in <module>
    uvicorn.run(
    │       └ <function run at 0x71fba308dda0>
    └ <module 'uvicorn' from '/usr/local/lib/python3.11/site-packages/uvicorn/__init__.py'>

  File "/usr/local/lib/python3.11/site-packages/uvicorn/main.py", line 580, in run
    server.run()
    │      └ <function Server.run at 0x71fba308df80>
    └ <uvicorn.server.Server object at 0x71fba4f63d90>
  File "/usr/local/lib/python3.11/site-packages/uvicorn/server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x71fba308e020>
           │       │   └ <uvicorn.server.Server object at 0x71fba4f63d90>
           │       └ <function run at 0x71fba7acd620>
           └ <module 'asyncio' from '/usr/local/lib/python3.11/asyncio/__init__.py'>
  File "/usr/local/lib/python3.11/asyncio/runners.py", line 190, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x71fba30773d0>
           │      └ <function Runner.run at 0x71fba73d0400>
           └ <asyncio.runners.Runner object at 0x71fba32e9910>
  File "/usr/local/lib/python3.11/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at /usr/local/lib/python3.11/site-packages/uvicorn/server.py:71> wai...
           │    │     └ <cyfunction Loop.run_until_complete at 0x71fba305fe00>
           │    └ <uvloop.Loop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x71fba32e9910>
  File "/usr/local/lib/python3.11/site-packages/starlette/middleware/base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x71fb923f0220>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fb923f00e0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │    └ <starlette.middleware.trustedhost.TrustedHostMiddleware object at 0x71fba32e8e50>
          └ <middleware.error_handling.ErrorHandlingMiddleware object at 0x71fba34af090>
  File "/usr/local/lib/python3.11/site-packages/starlette/middleware/trustedhost.py", line 51, in __call__
    await self.app(scope, receive, send)
          │    │   │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x71fb923f0220>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fb923f00e0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │    └ <starlette.middleware.cors.CORSMiddleware object at 0x71fba30cf910>
          └ <starlette.middleware.trustedhost.TrustedHostMiddleware object at 0x71fba32e8e50>
  File "/usr/local/lib/python3.11/site-packages/starlette/middleware/cors.py", line 85, in __call__
    await self.app(scope, receive, send)
          │    │   │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x71fb923f0220>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fb923f00e0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x71fba34a0990>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x71fba30cf910>
  File "/usr/local/lib/python3.11/site-packages/starlette/middleware/exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x71fb923f0220>
          │                            │    │    │     │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fb923f00e0>
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │                            │    │    └ <starlette.requests.Request object at 0x71fba30daf10>
          │                            │    └ <fastapi.routing.APIRouter object at 0x71fba2ad7950>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x71fba34a0990>
          └ <function wrap_app_handling_exceptions at 0x71fba64e1f80>
  File "/usr/local/lib/python3.11/site-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x71fba2a56160>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fb923f00e0>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          └ <fastapi.routing.APIRouter object at 0x71fba2ad7950>
  File "/usr/local/lib/python3.11/site-packages/starlette/routing.py", line 714, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x71fba2a56160>
          │    │                │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fb923f00e0>
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x71fba2ad7950>>
          └ <fastapi.routing.APIRouter object at 0x71fba2ad7950>
  File "/usr/local/lib/python3.11/site-packages/starlette/routing.py", line 734, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x71fba2a56160>
          │     │      │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fb923f00e0>
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │     └ <function Route.handle at 0x71fba64e3560>
          └ APIRoute(path='/api/v1/scraping/story-info', name='scrape_story_info', methods=['POST'])
  File "/usr/local/lib/python3.11/site-packages/starlette/routing.py", line 288, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x71fba2a56160>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fb923f00e0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │    └ <function request_response.<locals>.app at 0x71fba2ae8180>
          └ APIRoute(path='/api/v1/scraping/story-info', name='scrape_story_info', methods=['POST'])
  File "/usr/local/lib/python3.11/site-packages/starlette/routing.py", line 76, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
          │                            │    │        │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x71fba2a56160>
          │                            │    │        │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fb923f00e0>
          │                            │    │        └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │                            │    └ <starlette.requests.Request object at 0x71fb925a0f10>
          │                            └ <function request_response.<locals>.app.<locals>.app at 0x71fb92393f60>
          └ <function wrap_app_handling_exceptions at 0x71fba64e1f80>
  File "/usr/local/lib/python3.11/site-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x71fb923936a0>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fb923f00e0>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          └ <function request_response.<locals>.app.<locals>.app at 0x71fb92393f60>
  File "/usr/local/lib/python3.11/site-packages/starlette/routing.py", line 73, in app
    response = await f(request)
                     │ └ <starlette.requests.Request object at 0x71fb925a0f10>
                     └ <function get_request_handler.<locals>.app at 0x71fba2ae80e0>
  File "/usr/local/lib/python3.11/site-packages/fastapi/routing.py", line 301, in app
    raw_response = await run_endpoint_function(
                         └ <function run_endpoint_function at 0x71fba7ecb600>
  File "/usr/local/lib/python3.11/site-packages/fastapi/routing.py", line 212, in run_endpoint_function
    return await dependant.call(**values)
                 │         │      └ {'request': StoryInfoRequest(story_url=HttpUrl('https://webtruyen.diendantruyen.com/truyen/vo-dao-truong-sinh-ta-tu-hanh-co-k...
                 │         └ <function scrape_story_info at 0x71fba317eb60>
                 └ Dependant(path_params=[], query_params=[], header_params=[], cookie_params=[], body_params=[ModelField(field_info=Body(Pydant...

  File "/app/API/routers/scraping.py", line 459, in scrape_story_info
    return await controller.scrape_story_info(request, background_tasks)
                 │          │                 │        └ <fastapi.background.BackgroundTasks object at 0x71fb923c1d10>
                 │          │                 └ StoryInfoRequest(story_url=HttpUrl('https://webtruyen.diendantruyen.com/truyen/vo-dao-truong-sinh-ta-tu-hanh-co-kinh-nghiem')...
                 │          └ <function ScrapingController.scrape_story_info at 0x71fba4f02480>
                 └ <routers.scraping.ScrapingController object at 0x71fba32e89d0>

  File "/app/API/routers/scraping.py", line 54, in scrape_story_info
    story_data = await self.scraping_service.scrape_story_info(
                       │    │                └ <function ScrapingService.scrape_story_info at 0x71fba4f027a0>
                       │    └ <services.scraping_service.ScrapingService object at 0x71fba32eac10>
                       └ <routers.scraping.ScrapingController object at 0x71fba32e89d0>

  File "/app/API/services/scraping_service.py", line 104, in scrape_story_info
    await self._initialize()
          │    └ <function ScrapingService._initialize at 0x71fba4f02660>
          └ <services.scraping_service.ScrapingService object at 0x71fba32eac10>

> File "/app/API/services/scraping_service.py", line 62, in _initialize
    await self._scraper.start()
          │    │        └ <function MetruyenScraper.start at 0x71fb92588cc0>
          │    └ <src.metruyenscraper.MetruyenScraper object at 0x71fb925a05d0>
          └ <services.scraping_service.ScrapingService object at 0x71fba32eac10>

  File "/app/Scraper/src/metruyenscraper.py", line 63, in start
    await self.scraper_engine.start()
          │    │              └ <function ScraperEngine.start at 0x71fb92573880>
          │    └ <src.scraper_engine.ScraperEngine object at 0x71fba34ac890>
          └ <src.metruyenscraper.MetruyenScraper object at 0x71fb925a05d0>

  File "/app/Scraper/src/scraper_engine.py", line 95, in start
    self.browser = await playwright.chromium.launch(
    │    │               │          └ <property object at 0x71fb925429d0>
    │    │               └ <playwright._impl._playwright.Playwright object at 0x71fb915f8050>
    │    └ None
    └ <src.scraper_engine.ScraperEngine object at 0x71fba34ac890>

  File "/usr/local/lib/python3.11/site-packages/playwright/async_api/_generated.py", line 14438, in launch
    await self._impl_obj.launch(
          │    │         └ <function BrowserType.launch at 0x71fb9251b4c0>
          │    └ <BrowserType name=chromium executable_path=/home/<USER>/.cache/ms-playwright/chromium-1179/chrome-linux/chrome>
          └ <BrowserType name=chromium executable_path=/home/<USER>/.cache/ms-playwright/chromium-1179/chrome-linux/chrome>
  File "/usr/local/lib/python3.11/site-packages/playwright/_impl/_browser_type.py", line 98, in launch
    await self._channel.send(
          │    │        └ <function Channel.send at 0x71fb927b4180>
          │    └ <playwright._impl._connection.Channel object at 0x71fb915e6fd0>
          └ <BrowserType name=chromium executable_path=/home/<USER>/.cache/ms-playwright/chromium-1179/chrome-linux/chrome>
  File "/usr/local/lib/python3.11/site-packages/playwright/_impl/_connection.py", line 69, in send
    return await self._connection.wrap_api_call(
                 │    │           └ <function Connection.wrap_api_call at 0x71fb92624d60>
                 │    └ <playwright._impl._connection.Connection object at 0x71fb915e5990>
                 └ <playwright._impl._connection.Channel object at 0x71fb915e6fd0>
  File "/usr/local/lib/python3.11/site-packages/playwright/_impl/_connection.py", line 558, in wrap_api_call
    raise rewrite_error(error, f"{parsed_st['apiName']}: {error}") from None
          └ <function rewrite_error at 0x71fb9275c180>

playwright._impl._errors.Error: BrowserType.launch: Executable doesn't exist at /home/<USER>/.cache/ms-playwright/chromium_headless_shell-1179/chrome-linux/headless_shell
╔════════════════════════════════════════════════════════════╗
║ Looks like Playwright was just installed or updated.       ║
║ Please run the following command to download new browsers: ║
║                                                            ║
║     playwright install                                     ║
║                                                            ║
║ <3 Playwright Team                                         ║
╚════════════════════════════════════════════════════════════╝
2025-07-09 08:48:57 | ERROR    | logging:callHandlers:1706 | Error scraping story info: Scraping service initialization failed: BrowserType.launch: Executable doesn't exist at /home/<USER>/.cache/ms-playwright/chromium_headless_shell-1179/chrome-linux/headless_shell
╔════════════════════════════════════════════════════════════╗
║ Looks like Playwright was just installed or updated.       ║
║ Please run the following command to download new browsers: ║
║                                                            ║
║     playwright install                                     ║
║                                                            ║
║ <3 Playwright Team                                         ║
╚════════════════════════════════════════════════════════════╝
Traceback (most recent call last):

  File "/app/API/services/scraping_service.py", line 62, in _initialize
    await self._scraper.start()
          │    │        └ <function MetruyenScraper.start at 0x71fb92588cc0>
          │    └ <src.metruyenscraper.MetruyenScraper object at 0x71fb925a05d0>
          └ <services.scraping_service.ScrapingService object at 0x71fba32eac10>

  File "/app/Scraper/src/metruyenscraper.py", line 63, in start
    await self.scraper_engine.start()
          │    │              └ <function ScraperEngine.start at 0x71fb92573880>
          │    └ <src.scraper_engine.ScraperEngine object at 0x71fba34ac890>
          └ <src.metruyenscraper.MetruyenScraper object at 0x71fb925a05d0>

  File "/app/Scraper/src/scraper_engine.py", line 95, in start
    self.browser = await playwright.chromium.launch(
    │    │               │          └ <property object at 0x71fb925429d0>
    │    │               └ <playwright._impl._playwright.Playwright object at 0x71fb915f8050>
    │    └ None
    └ <src.scraper_engine.ScraperEngine object at 0x71fba34ac890>

  File "/usr/local/lib/python3.11/site-packages/playwright/async_api/_generated.py", line 14438, in launch
    await self._impl_obj.launch(
          │    │         └ <function BrowserType.launch at 0x71fb9251b4c0>
          │    └ <BrowserType name=chromium executable_path=/home/<USER>/.cache/ms-playwright/chromium-1179/chrome-linux/chrome>
          └ <BrowserType name=chromium executable_path=/home/<USER>/.cache/ms-playwright/chromium-1179/chrome-linux/chrome>
  File "/usr/local/lib/python3.11/site-packages/playwright/_impl/_browser_type.py", line 98, in launch
    await self._channel.send(
          │    │        └ <function Channel.send at 0x71fb927b4180>
          │    └ <playwright._impl._connection.Channel object at 0x71fb915e6fd0>
          └ <BrowserType name=chromium executable_path=/home/<USER>/.cache/ms-playwright/chromium-1179/chrome-linux/chrome>
  File "/usr/local/lib/python3.11/site-packages/playwright/_impl/_connection.py", line 69, in send
    return await self._connection.wrap_api_call(
                 │    │           └ <function Connection.wrap_api_call at 0x71fb92624d60>
                 │    └ <playwright._impl._connection.Connection object at 0x71fb915e5990>
                 └ <playwright._impl._connection.Channel object at 0x71fb915e6fd0>
  File "/usr/local/lib/python3.11/site-packages/playwright/_impl/_connection.py", line 558, in wrap_api_call
    raise rewrite_error(error, f"{parsed_st['apiName']}: {error}") from None
          └ <function rewrite_error at 0x71fb9275c180>

playwright._impl._errors.Error: BrowserType.launch: Executable doesn't exist at /home/<USER>/.cache/ms-playwright/chromium_headless_shell-1179/chrome-linux/headless_shell
╔════════════════════════════════════════════════════════════╗
║ Looks like Playwright was just installed or updated.       ║
║ Please run the following command to download new browsers: ║
║                                                            ║
║     playwright install                                     ║
║                                                            ║
║ <3 Playwright Team                                         ║
╚════════════════════════════════════════════════════════════╝


During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "/app/API/main.py", line 288, in <module>
    uvicorn.run(
    │       └ <function run at 0x71fba308dda0>
    └ <module 'uvicorn' from '/usr/local/lib/python3.11/site-packages/uvicorn/__init__.py'>

  File "/usr/local/lib/python3.11/site-packages/uvicorn/main.py", line 580, in run
    server.run()
    │      └ <function Server.run at 0x71fba308df80>
    └ <uvicorn.server.Server object at 0x71fba4f63d90>
  File "/usr/local/lib/python3.11/site-packages/uvicorn/server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x71fba308e020>
           │       │   └ <uvicorn.server.Server object at 0x71fba4f63d90>
           │       └ <function run at 0x71fba7acd620>
           └ <module 'asyncio' from '/usr/local/lib/python3.11/asyncio/__init__.py'>
  File "/usr/local/lib/python3.11/asyncio/runners.py", line 190, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x71fba30773d0>
           │      └ <function Runner.run at 0x71fba73d0400>
           └ <asyncio.runners.Runner object at 0x71fba32e9910>
  File "/usr/local/lib/python3.11/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at /usr/local/lib/python3.11/site-packages/uvicorn/server.py:71> wai...
           │    │     └ <cyfunction Loop.run_until_complete at 0x71fba305fe00>
           │    └ <uvloop.Loop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x71fba32e9910>
  File "/usr/local/lib/python3.11/site-packages/starlette/middleware/base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x71fb923f0220>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fb923f00e0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │    └ <starlette.middleware.trustedhost.TrustedHostMiddleware object at 0x71fba32e8e50>
          └ <middleware.error_handling.ErrorHandlingMiddleware object at 0x71fba34af090>
  File "/usr/local/lib/python3.11/site-packages/starlette/middleware/trustedhost.py", line 51, in __call__
    await self.app(scope, receive, send)
          │    │   │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x71fb923f0220>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fb923f00e0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │    └ <starlette.middleware.cors.CORSMiddleware object at 0x71fba30cf910>
          └ <starlette.middleware.trustedhost.TrustedHostMiddleware object at 0x71fba32e8e50>
  File "/usr/local/lib/python3.11/site-packages/starlette/middleware/cors.py", line 85, in __call__
    await self.app(scope, receive, send)
          │    │   │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x71fb923f0220>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fb923f00e0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x71fba34a0990>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x71fba30cf910>
  File "/usr/local/lib/python3.11/site-packages/starlette/middleware/exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x71fb923f0220>
          │                            │    │    │     │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fb923f00e0>
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │                            │    │    └ <starlette.requests.Request object at 0x71fba30daf10>
          │                            │    └ <fastapi.routing.APIRouter object at 0x71fba2ad7950>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x71fba34a0990>
          └ <function wrap_app_handling_exceptions at 0x71fba64e1f80>
  File "/usr/local/lib/python3.11/site-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x71fba2a56160>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fb923f00e0>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          └ <fastapi.routing.APIRouter object at 0x71fba2ad7950>
  File "/usr/local/lib/python3.11/site-packages/starlette/routing.py", line 714, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x71fba2a56160>
          │    │                │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fb923f00e0>
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x71fba2ad7950>>
          └ <fastapi.routing.APIRouter object at 0x71fba2ad7950>
  File "/usr/local/lib/python3.11/site-packages/starlette/routing.py", line 734, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x71fba2a56160>
          │     │      │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fb923f00e0>
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │     └ <function Route.handle at 0x71fba64e3560>
          └ APIRoute(path='/api/v1/scraping/story-info', name='scrape_story_info', methods=['POST'])
  File "/usr/local/lib/python3.11/site-packages/starlette/routing.py", line 288, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x71fba2a56160>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fb923f00e0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │    └ <function request_response.<locals>.app at 0x71fba2ae8180>
          └ APIRoute(path='/api/v1/scraping/story-info', name='scrape_story_info', methods=['POST'])
  File "/usr/local/lib/python3.11/site-packages/starlette/routing.py", line 76, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
          │                            │    │        │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x71fba2a56160>
          │                            │    │        │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fb923f00e0>
          │                            │    │        └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │                            │    └ <starlette.requests.Request object at 0x71fb925a0f10>
          │                            └ <function request_response.<locals>.app.<locals>.app at 0x71fb92393f60>
          └ <function wrap_app_handling_exceptions at 0x71fba64e1f80>
  File "/usr/local/lib/python3.11/site-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x71fb923936a0>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fb923f00e0>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          └ <function request_response.<locals>.app.<locals>.app at 0x71fb92393f60>
  File "/usr/local/lib/python3.11/site-packages/starlette/routing.py", line 73, in app
    response = await f(request)
                     │ └ <starlette.requests.Request object at 0x71fb925a0f10>
                     └ <function get_request_handler.<locals>.app at 0x71fba2ae80e0>
  File "/usr/local/lib/python3.11/site-packages/fastapi/routing.py", line 301, in app
    raw_response = await run_endpoint_function(
                         └ <function run_endpoint_function at 0x71fba7ecb600>
  File "/usr/local/lib/python3.11/site-packages/fastapi/routing.py", line 212, in run_endpoint_function
    return await dependant.call(**values)
                 │         │      └ {'request': StoryInfoRequest(story_url=HttpUrl('https://webtruyen.diendantruyen.com/truyen/vo-dao-truong-sinh-ta-tu-hanh-co-k...
                 │         └ <function scrape_story_info at 0x71fba317eb60>
                 └ Dependant(path_params=[], query_params=[], header_params=[], cookie_params=[], body_params=[ModelField(field_info=Body(Pydant...

  File "/app/API/routers/scraping.py", line 459, in scrape_story_info
    return await controller.scrape_story_info(request, background_tasks)
                 │          │                 │        └ <fastapi.background.BackgroundTasks object at 0x71fb923c1d10>
                 │          │                 └ StoryInfoRequest(story_url=HttpUrl('https://webtruyen.diendantruyen.com/truyen/vo-dao-truong-sinh-ta-tu-hanh-co-kinh-nghiem')...
                 │          └ <function ScrapingController.scrape_story_info at 0x71fba4f02480>
                 └ <routers.scraping.ScrapingController object at 0x71fba32e89d0>

> File "/app/API/routers/scraping.py", line 54, in scrape_story_info
    story_data = await self.scraping_service.scrape_story_info(
                       │    │                └ <function ScrapingService.scrape_story_info at 0x71fba4f027a0>
                       │    └ <services.scraping_service.ScrapingService object at 0x71fba32eac10>
                       └ <routers.scraping.ScrapingController object at 0x71fba32e89d0>

  File "/app/API/services/scraping_service.py", line 104, in scrape_story_info
    await self._initialize()
          │    └ <function ScrapingService._initialize at 0x71fba4f02660>
          └ <services.scraping_service.ScrapingService object at 0x71fba32eac10>

  File "/app/API/services/scraping_service.py", line 73, in _initialize
    raise ScrapingError(f"Scraping service initialization failed: {str(e) if e else 'Unknown error'}")
          └ <class 'middleware.error_handling.ScrapingError'>

middleware.error_handling.ScrapingError: Scraping service initialization failed: BrowserType.launch: Executable doesn't exist at /home/<USER>/.cache/ms-playwright/chromium_headless_shell-1179/chrome-linux/headless_shell
╔════════════════════════════════════════════════════════════╗
║ Looks like Playwright was just installed or updated.       ║
║ Please run the following command to download new browsers: ║
║                                                            ║
║     playwright install                                     ║
║                                                            ║
║ <3 Playwright Team                                         ║
╚════════════════════════════════════════════════════════════╝

Traceback (most recent call last):

  File "/app/API/services/scraping_service.py", line 62, in _initialize
    await self._scraper.start()
          │    │        └ <function MetruyenScraper.start at 0x71fb92588cc0>
          │    └ <src.metruyenscraper.MetruyenScraper object at 0x71fb925a05d0>
          └ <services.scraping_service.ScrapingService object at 0x71fba32eac10>

  File "/app/Scraper/src/metruyenscraper.py", line 63, in start
    await self.scraper_engine.start()
          │    │              └ <function ScraperEngine.start at 0x71fb92573880>
          │    └ <src.scraper_engine.ScraperEngine object at 0x71fba34ac890>
          └ <src.metruyenscraper.MetruyenScraper object at 0x71fb925a05d0>

  File "/app/Scraper/src/scraper_engine.py", line 95, in start
    self.browser = await playwright.chromium.launch(
    │    │               │          └ <property object at 0x71fb925429d0>
    │    │               └ <playwright._impl._playwright.Playwright object at 0x71fb915f8050>
    │    └ None
    └ <src.scraper_engine.ScraperEngine object at 0x71fba34ac890>

  File "/usr/local/lib/python3.11/site-packages/playwright/async_api/_generated.py", line 14438, in launch
    await self._impl_obj.launch(
          │    │         └ <function BrowserType.launch at 0x71fb9251b4c0>
          │    └ <BrowserType name=chromium executable_path=/home/<USER>/.cache/ms-playwright/chromium-1179/chrome-linux/chrome>
          └ <BrowserType name=chromium executable_path=/home/<USER>/.cache/ms-playwright/chromium-1179/chrome-linux/chrome>
  File "/usr/local/lib/python3.11/site-packages/playwright/_impl/_browser_type.py", line 98, in launch
    await self._channel.send(
          │    │        └ <function Channel.send at 0x71fb927b4180>
          │    └ <playwright._impl._connection.Channel object at 0x71fb915e6fd0>
          └ <BrowserType name=chromium executable_path=/home/<USER>/.cache/ms-playwright/chromium-1179/chrome-linux/chrome>
  File "/usr/local/lib/python3.11/site-packages/playwright/_impl/_connection.py", line 69, in send
    return await self._connection.wrap_api_call(
                 │    │           └ <function Connection.wrap_api_call at 0x71fb92624d60>
                 │    └ <playwright._impl._connection.Connection object at 0x71fb915e5990>
                 └ <playwright._impl._connection.Channel object at 0x71fb915e6fd0>
  File "/usr/local/lib/python3.11/site-packages/playwright/_impl/_connection.py", line 558, in wrap_api_call
    raise rewrite_error(error, f"{parsed_st['apiName']}: {error}") from None
          └ <function rewrite_error at 0x71fb9275c180>

playwright._impl._errors.Error: BrowserType.launch: Executable doesn't exist at /home/<USER>/.cache/ms-playwright/chromium_headless_shell-1179/chrome-linux/headless_shell
╔════════════════════════════════════════════════════════════╗
║ Looks like Playwright was just installed or updated.       ║
║ Please run the following command to download new browsers: ║
║                                                            ║
║     playwright install                                     ║
║                                                            ║
║ <3 Playwright Team                                         ║
╚════════════════════════════════════════════════════════════╝


During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "/app/API/main.py", line 288, in <module>
    uvicorn.run(
    │       └ <function run at 0x71fba308dda0>
    └ <module 'uvicorn' from '/usr/local/lib/python3.11/site-packages/uvicorn/__init__.py'>

  File "/usr/local/lib/python3.11/site-packages/uvicorn/main.py", line 580, in run
    server.run()
    │      └ <function Server.run at 0x71fba308df80>
    └ <uvicorn.server.Server object at 0x71fba4f63d90>
  File "/usr/local/lib/python3.11/site-packages/uvicorn/server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x71fba308e020>
           │       │   └ <uvicorn.server.Server object at 0x71fba4f63d90>
           │       └ <function run at 0x71fba7acd620>
           └ <module 'asyncio' from '/usr/local/lib/python3.11/asyncio/__init__.py'>
  File "/usr/local/lib/python3.11/asyncio/runners.py", line 190, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x71fba30773d0>
           │      └ <function Runner.run at 0x71fba73d0400>
           └ <asyncio.runners.Runner object at 0x71fba32e9910>
  File "/usr/local/lib/python3.11/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at /usr/local/lib/python3.11/site-packages/uvicorn/server.py:71> wai...
           │    │     └ <cyfunction Loop.run_until_complete at 0x71fba305fe00>
           │    └ <uvloop.Loop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x71fba32e9910>
  File "/usr/local/lib/python3.11/site-packages/starlette/middleware/base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x71fb923f0220>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fb923f00e0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │    └ <starlette.middleware.trustedhost.TrustedHostMiddleware object at 0x71fba32e8e50>
          └ <middleware.error_handling.ErrorHandlingMiddleware object at 0x71fba34af090>
  File "/usr/local/lib/python3.11/site-packages/starlette/middleware/trustedhost.py", line 51, in __call__
    await self.app(scope, receive, send)
          │    │   │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x71fb923f0220>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fb923f00e0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │    └ <starlette.middleware.cors.CORSMiddleware object at 0x71fba30cf910>
          └ <starlette.middleware.trustedhost.TrustedHostMiddleware object at 0x71fba32e8e50>
  File "/usr/local/lib/python3.11/site-packages/starlette/middleware/cors.py", line 85, in __call__
    await self.app(scope, receive, send)
          │    │   │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x71fb923f0220>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fb923f00e0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x71fba34a0990>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x71fba30cf910>
  File "/usr/local/lib/python3.11/site-packages/starlette/middleware/exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x71fb923f0220>
          │                            │    │    │     │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fb923f00e0>
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │                            │    │    └ <starlette.requests.Request object at 0x71fba30daf10>
          │                            │    └ <fastapi.routing.APIRouter object at 0x71fba2ad7950>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x71fba34a0990>
          └ <function wrap_app_handling_exceptions at 0x71fba64e1f80>
  File "/usr/local/lib/python3.11/site-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x71fba2a56160>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fb923f00e0>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          └ <fastapi.routing.APIRouter object at 0x71fba2ad7950>
  File "/usr/local/lib/python3.11/site-packages/starlette/routing.py", line 714, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x71fba2a56160>
          │    │                │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fb923f00e0>
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x71fba2ad7950>>
          └ <fastapi.routing.APIRouter object at 0x71fba2ad7950>
  File "/usr/local/lib/python3.11/site-packages/starlette/routing.py", line 734, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x71fba2a56160>
          │     │      │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fb923f00e0>
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │     └ <function Route.handle at 0x71fba64e3560>
          └ APIRoute(path='/api/v1/scraping/story-info', name='scrape_story_info', methods=['POST'])
  File "/usr/local/lib/python3.11/site-packages/starlette/routing.py", line 288, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x71fba2a56160>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fb923f00e0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │    └ <function request_response.<locals>.app at 0x71fba2ae8180>
          └ APIRoute(path='/api/v1/scraping/story-info', name='scrape_story_info', methods=['POST'])
  File "/usr/local/lib/python3.11/site-packages/starlette/routing.py", line 76, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
          │                            │    │        │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x71fba2a56160>
          │                            │    │        │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fb923f00e0>
          │                            │    │        └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │                            │    └ <starlette.requests.Request object at 0x71fb925a0f10>
          │                            └ <function request_response.<locals>.app.<locals>.app at 0x71fb92393f60>
          └ <function wrap_app_handling_exceptions at 0x71fba64e1f80>
  File "/usr/local/lib/python3.11/site-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x71fb923936a0>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fb923f00e0>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          └ <function request_response.<locals>.app.<locals>.app at 0x71fb92393f60>
  File "/usr/local/lib/python3.11/site-packages/starlette/routing.py", line 73, in app
    response = await f(request)
                     │ └ <starlette.requests.Request object at 0x71fb925a0f10>
                     └ <function get_request_handler.<locals>.app at 0x71fba2ae80e0>
  File "/usr/local/lib/python3.11/site-packages/fastapi/routing.py", line 301, in app
    raw_response = await run_endpoint_function(
                         └ <function run_endpoint_function at 0x71fba7ecb600>
  File "/usr/local/lib/python3.11/site-packages/fastapi/routing.py", line 212, in run_endpoint_function
    return await dependant.call(**values)
                 │         │      └ {'request': StoryInfoRequest(story_url=HttpUrl('https://webtruyen.diendantruyen.com/truyen/vo-dao-truong-sinh-ta-tu-hanh-co-k...
                 │         └ <function scrape_story_info at 0x71fba317eb60>
                 └ Dependant(path_params=[], query_params=[], header_params=[], cookie_params=[], body_params=[ModelField(field_info=Body(Pydant...

  File "/app/API/routers/scraping.py", line 459, in scrape_story_info
    return await controller.scrape_story_info(request, background_tasks)
                 │          │                 │        └ <fastapi.background.BackgroundTasks object at 0x71fb923c1d10>
                 │          │                 └ StoryInfoRequest(story_url=HttpUrl('https://webtruyen.diendantruyen.com/truyen/vo-dao-truong-sinh-ta-tu-hanh-co-kinh-nghiem')...
                 │          └ <function ScrapingController.scrape_story_info at 0x71fba4f02480>
                 └ <routers.scraping.ScrapingController object at 0x71fba32e89d0>

> File "/app/API/routers/scraping.py", line 54, in scrape_story_info
    story_data = await self.scraping_service.scrape_story_info(
                       │    │                └ <function ScrapingService.scrape_story_info at 0x71fba4f027a0>
                       │    └ <services.scraping_service.ScrapingService object at 0x71fba32eac10>
                       └ <routers.scraping.ScrapingController object at 0x71fba32e89d0>

  File "/app/API/services/scraping_service.py", line 104, in scrape_story_info
    await self._initialize()
          │    └ <function ScrapingService._initialize at 0x71fba4f02660>
          └ <services.scraping_service.ScrapingService object at 0x71fba32eac10>

  File "/app/API/services/scraping_service.py", line 73, in _initialize
    raise ScrapingError(f"Scraping service initialization failed: {str(e) if e else 'Unknown error'}")
          └ <class 'middleware.error_handling.ScrapingError'>

middleware.error_handling.ScrapingError: Scraping service initialization failed: BrowserType.launch: Executable doesn't exist at /home/<USER>/.cache/ms-playwright/chromium_headless_shell-1179/chrome-linux/headless_shell
╔════════════════════════════════════════════════════════════╗
║ Looks like Playwright was just installed or updated.       ║
║ Please run the following command to download new browsers: ║
║                                                            ║
║     playwright install                                     ║
║                                                            ║
║ <3 Playwright Team                                         ║
╚════════════════════════════════════════════════════════════╝
2025-07-09 08:48:57 | ERROR    | logging:callHandlers:1706 | Scraping Error: Failed to scrape story information: Scraping service initialization failed: BrowserType.launch: Executable doesn't exist at /home/<USER>/.cache/ms-playwright/chromium_headless_shell-1179/chrome-linux/headless_shell
╔════════════════════════════════════════════════════════════╗
║ Looks like Playwright was just installed or updated.       ║
║ Please run the following command to download new browsers: ║
║                                                            ║
║     playwright install                                     ║
║                                                            ║
║ <3 Playwright Team                                         ║
╚════════════════════════════════════════════════════════════╝ - Path: /api/v1/scraping/story-info - Method: POST
Traceback (most recent call last):

  File "/usr/local/lib/python3.11/site-packages/starlette/middleware/base.py", line 148, in call_next
    message = await recv_stream.receive()
                    │           └ <function MemoryObjectReceiveStream.receive at 0x71fba6723d80>
                    └ MemoryObjectReceiveStream(_state=MemoryObjectStreamState(max_buffer_size=0, buffer=deque([]), open_send_channels=0, open_rece...
  File "/usr/local/lib/python3.11/site-packages/anyio/streams/memory.py", line 126, in receive
    raise EndOfStream from None
          └ <class 'anyio.EndOfStream'>

anyio.EndOfStream


During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "/app/API/main.py", line 288, in <module>
    uvicorn.run(
    │       └ <function run at 0x71fba308dda0>
    └ <module 'uvicorn' from '/usr/local/lib/python3.11/site-packages/uvicorn/__init__.py'>

  File "/usr/local/lib/python3.11/site-packages/uvicorn/main.py", line 580, in run
    server.run()
    │      └ <function Server.run at 0x71fba308df80>
    └ <uvicorn.server.Server object at 0x71fba4f63d90>
  File "/usr/local/lib/python3.11/site-packages/uvicorn/server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x71fba308e020>
           │       │   └ <uvicorn.server.Server object at 0x71fba4f63d90>
           │       └ <function run at 0x71fba7acd620>
           └ <module 'asyncio' from '/usr/local/lib/python3.11/asyncio/__init__.py'>
  File "/usr/local/lib/python3.11/asyncio/runners.py", line 190, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x71fba30773d0>
           │      └ <function Runner.run at 0x71fba73d0400>
           └ <asyncio.runners.Runner object at 0x71fba32e9910>
  File "/usr/local/lib/python3.11/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at /usr/local/lib/python3.11/site-packages/uvicorn/server.py:71> wai...
           │    │     └ <cyfunction Loop.run_until_complete at 0x71fba305fe00>
           │    └ <uvloop.Loop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x71fba32e9910>
  File "/usr/local/lib/python3.11/site-packages/starlette/middleware/base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x71fb92393600>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fb92392d40>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │    └ <middleware.error_handling.ErrorHandlingMiddleware object at 0x71fba34af090>
          └ <middleware.rate_limiting.RateLimitingMiddleware object at 0x71fba2b17c50>
  File "/usr/local/lib/python3.11/site-packages/starlette/middleware/base.py", line 178, in __call__
    response = await self.dispatch_func(request, call_next)
                     │    │             │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x71fb92393420>
                     │    │             └ <starlette.middleware.base._CachedRequest object at 0x71fb923ea590>
                     │    └ <bound method ErrorHandlingMiddleware.dispatch of <middleware.error_handling.ErrorHandlingMiddleware object at 0x71fba34af090>>
                     └ <middleware.error_handling.ErrorHandlingMiddleware object at 0x71fba34af090>

> File "/app/API/middleware/error_handling.py", line 31, in dispatch
    response = await call_next(request)
                     │         └ <starlette.middleware.base._CachedRequest object at 0x71fb923ea590>
                     └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x71fb92393420>

  File "/usr/local/lib/python3.11/site-packages/starlette/middleware/base.py", line 156, in call_next
    raise app_exc
          └ ScrapingError("Failed to scrape story information: Scraping service initialization failed: BrowserType.launch: Executable doe...
  File "/usr/local/lib/python3.11/site-packages/starlette/middleware/base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x71fb923f0220>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fb923f00e0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │    └ <starlette.middleware.trustedhost.TrustedHostMiddleware object at 0x71fba32e8e50>
          └ <middleware.error_handling.ErrorHandlingMiddleware object at 0x71fba34af090>
  File "/usr/local/lib/python3.11/site-packages/starlette/middleware/trustedhost.py", line 51, in __call__
    await self.app(scope, receive, send)
          │    │   │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x71fb923f0220>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fb923f00e0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │    └ <starlette.middleware.cors.CORSMiddleware object at 0x71fba30cf910>
          └ <starlette.middleware.trustedhost.TrustedHostMiddleware object at 0x71fba32e8e50>
  File "/usr/local/lib/python3.11/site-packages/starlette/middleware/cors.py", line 85, in __call__
    await self.app(scope, receive, send)
          │    │   │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x71fb923f0220>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fb923f00e0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x71fba34a0990>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x71fba30cf910>
  File "/usr/local/lib/python3.11/site-packages/starlette/middleware/exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x71fb923f0220>
          │                            │    │    │     │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fb923f00e0>
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │                            │    │    └ <starlette.requests.Request object at 0x71fba30daf10>
          │                            │    └ <fastapi.routing.APIRouter object at 0x71fba2ad7950>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x71fba34a0990>
          └ <function wrap_app_handling_exceptions at 0x71fba64e1f80>
  File "/usr/local/lib/python3.11/site-packages/starlette/_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "/usr/local/lib/python3.11/site-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x71fba2a56160>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fb923f00e0>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          └ <fastapi.routing.APIRouter object at 0x71fba2ad7950>
  File "/usr/local/lib/python3.11/site-packages/starlette/routing.py", line 714, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x71fba2a56160>
          │    │                │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fb923f00e0>
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x71fba2ad7950>>
          └ <fastapi.routing.APIRouter object at 0x71fba2ad7950>
  File "/usr/local/lib/python3.11/site-packages/starlette/routing.py", line 734, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x71fba2a56160>
          │     │      │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fb923f00e0>
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │     └ <function Route.handle at 0x71fba64e3560>
          └ APIRoute(path='/api/v1/scraping/story-info', name='scrape_story_info', methods=['POST'])
  File "/usr/local/lib/python3.11/site-packages/starlette/routing.py", line 288, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x71fba2a56160>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fb923f00e0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │    └ <function request_response.<locals>.app at 0x71fba2ae8180>
          └ APIRoute(path='/api/v1/scraping/story-info', name='scrape_story_info', methods=['POST'])
  File "/usr/local/lib/python3.11/site-packages/starlette/routing.py", line 76, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
          │                            │    │        │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x71fba2a56160>
          │                            │    │        │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fb923f00e0>
          │                            │    │        └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │                            │    └ <starlette.requests.Request object at 0x71fb925a0f10>
          │                            └ <function request_response.<locals>.app.<locals>.app at 0x71fb92393f60>
          └ <function wrap_app_handling_exceptions at 0x71fba64e1f80>
  File "/usr/local/lib/python3.11/site-packages/starlette/_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "/usr/local/lib/python3.11/site-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x71fb923936a0>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fb923f00e0>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          └ <function request_response.<locals>.app.<locals>.app at 0x71fb92393f60>
  File "/usr/local/lib/python3.11/site-packages/starlette/routing.py", line 73, in app
    response = await f(request)
                     │ └ <starlette.requests.Request object at 0x71fb925a0f10>
                     └ <function get_request_handler.<locals>.app at 0x71fba2ae80e0>
  File "/usr/local/lib/python3.11/site-packages/fastapi/routing.py", line 301, in app
    raw_response = await run_endpoint_function(
                         └ <function run_endpoint_function at 0x71fba7ecb600>
  File "/usr/local/lib/python3.11/site-packages/fastapi/routing.py", line 212, in run_endpoint_function
    return await dependant.call(**values)
                 │         │      └ {'request': StoryInfoRequest(story_url=HttpUrl('https://webtruyen.diendantruyen.com/truyen/vo-dao-truong-sinh-ta-tu-hanh-co-k...
                 │         └ <function scrape_story_info at 0x71fba317eb60>
                 └ Dependant(path_params=[], query_params=[], header_params=[], cookie_params=[], body_params=[ModelField(field_info=Body(Pydant...

  File "/app/API/routers/scraping.py", line 459, in scrape_story_info
    return await controller.scrape_story_info(request, background_tasks)
                 │          │                 │        └ <fastapi.background.BackgroundTasks object at 0x71fb923c1d10>
                 │          │                 └ StoryInfoRequest(story_url=HttpUrl('https://webtruyen.diendantruyen.com/truyen/vo-dao-truong-sinh-ta-tu-hanh-co-kinh-nghiem')...
                 │          └ <function ScrapingController.scrape_story_info at 0x71fba4f02480>
                 └ <routers.scraping.ScrapingController object at 0x71fba32e89d0>

  File "/app/API/routers/scraping.py", line 108, in scrape_story_info
    raise ScrapingError(f"Failed to scrape story information: {e}")
          └ <class 'middleware.error_handling.ScrapingError'>

middleware.error_handling.ScrapingError: Failed to scrape story information: Scraping service initialization failed: BrowserType.launch: Executable doesn't exist at /home/<USER>/.cache/ms-playwright/chromium_headless_shell-1179/chrome-linux/headless_shell
╔════════════════════════════════════════════════════════════╗
║ Looks like Playwright was just installed or updated.       ║
║ Please run the following command to download new browsers: ║
║                                                            ║
║     playwright install                                     ║
║                                                            ║
║ <3 Playwright Team                                         ║
╚════════════════════════════════════════════════════════════╝

Traceback (most recent call last):

  File "/usr/local/lib/python3.11/site-packages/starlette/middleware/base.py", line 148, in call_next
    message = await recv_stream.receive()
                    │           └ <function MemoryObjectReceiveStream.receive at 0x71fba6723d80>
                    └ MemoryObjectReceiveStream(_state=MemoryObjectStreamState(max_buffer_size=0, buffer=deque([]), open_send_channels=0, open_rece...
  File "/usr/local/lib/python3.11/site-packages/anyio/streams/memory.py", line 126, in receive
    raise EndOfStream from None
          └ <class 'anyio.EndOfStream'>

anyio.EndOfStream


During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "/app/API/main.py", line 288, in <module>
    uvicorn.run(
    │       └ <function run at 0x71fba308dda0>
    └ <module 'uvicorn' from '/usr/local/lib/python3.11/site-packages/uvicorn/__init__.py'>

  File "/usr/local/lib/python3.11/site-packages/uvicorn/main.py", line 580, in run
    server.run()
    │      └ <function Server.run at 0x71fba308df80>
    └ <uvicorn.server.Server object at 0x71fba4f63d90>
  File "/usr/local/lib/python3.11/site-packages/uvicorn/server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x71fba308e020>
           │       │   └ <uvicorn.server.Server object at 0x71fba4f63d90>
           │       └ <function run at 0x71fba7acd620>
           └ <module 'asyncio' from '/usr/local/lib/python3.11/asyncio/__init__.py'>
  File "/usr/local/lib/python3.11/asyncio/runners.py", line 190, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x71fba30773d0>
           │      └ <function Runner.run at 0x71fba73d0400>
           └ <asyncio.runners.Runner object at 0x71fba32e9910>
  File "/usr/local/lib/python3.11/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at /usr/local/lib/python3.11/site-packages/uvicorn/server.py:71> wai...
           │    │     └ <cyfunction Loop.run_until_complete at 0x71fba305fe00>
           │    └ <uvloop.Loop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x71fba32e9910>
  File "/usr/local/lib/python3.11/site-packages/starlette/middleware/base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x71fb92393600>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fb92392d40>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │    └ <middleware.error_handling.ErrorHandlingMiddleware object at 0x71fba34af090>
          └ <middleware.rate_limiting.RateLimitingMiddleware object at 0x71fba2b17c50>
  File "/usr/local/lib/python3.11/site-packages/starlette/middleware/base.py", line 178, in __call__
    response = await self.dispatch_func(request, call_next)
                     │    │             │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x71fb92393420>
                     │    │             └ <starlette.middleware.base._CachedRequest object at 0x71fb923ea590>
                     │    └ <bound method ErrorHandlingMiddleware.dispatch of <middleware.error_handling.ErrorHandlingMiddleware object at 0x71fba34af090>>
                     └ <middleware.error_handling.ErrorHandlingMiddleware object at 0x71fba34af090>

> File "/app/API/middleware/error_handling.py", line 31, in dispatch
    response = await call_next(request)
                     │         └ <starlette.middleware.base._CachedRequest object at 0x71fb923ea590>
                     └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x71fb92393420>

  File "/usr/local/lib/python3.11/site-packages/starlette/middleware/base.py", line 156, in call_next
    raise app_exc
          └ ScrapingError("Failed to scrape story information: Scraping service initialization failed: BrowserType.launch: Executable doe...
  File "/usr/local/lib/python3.11/site-packages/starlette/middleware/base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x71fb923f0220>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fb923f00e0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │    └ <starlette.middleware.trustedhost.TrustedHostMiddleware object at 0x71fba32e8e50>
          └ <middleware.error_handling.ErrorHandlingMiddleware object at 0x71fba34af090>
  File "/usr/local/lib/python3.11/site-packages/starlette/middleware/trustedhost.py", line 51, in __call__
    await self.app(scope, receive, send)
          │    │   │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x71fb923f0220>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fb923f00e0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │    └ <starlette.middleware.cors.CORSMiddleware object at 0x71fba30cf910>
          └ <starlette.middleware.trustedhost.TrustedHostMiddleware object at 0x71fba32e8e50>
  File "/usr/local/lib/python3.11/site-packages/starlette/middleware/cors.py", line 85, in __call__
    await self.app(scope, receive, send)
          │    │   │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x71fb923f0220>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fb923f00e0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x71fba34a0990>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x71fba30cf910>
  File "/usr/local/lib/python3.11/site-packages/starlette/middleware/exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x71fb923f0220>
          │                            │    │    │     │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fb923f00e0>
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │                            │    │    └ <starlette.requests.Request object at 0x71fba30daf10>
          │                            │    └ <fastapi.routing.APIRouter object at 0x71fba2ad7950>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x71fba34a0990>
          └ <function wrap_app_handling_exceptions at 0x71fba64e1f80>
  File "/usr/local/lib/python3.11/site-packages/starlette/_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "/usr/local/lib/python3.11/site-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x71fba2a56160>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fb923f00e0>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          └ <fastapi.routing.APIRouter object at 0x71fba2ad7950>
  File "/usr/local/lib/python3.11/site-packages/starlette/routing.py", line 714, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x71fba2a56160>
          │    │                │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fb923f00e0>
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x71fba2ad7950>>
          └ <fastapi.routing.APIRouter object at 0x71fba2ad7950>
  File "/usr/local/lib/python3.11/site-packages/starlette/routing.py", line 734, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x71fba2a56160>
          │     │      │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fb923f00e0>
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │     └ <function Route.handle at 0x71fba64e3560>
          └ APIRoute(path='/api/v1/scraping/story-info', name='scrape_story_info', methods=['POST'])
  File "/usr/local/lib/python3.11/site-packages/starlette/routing.py", line 288, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x71fba2a56160>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fb923f00e0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │    └ <function request_response.<locals>.app at 0x71fba2ae8180>
          └ APIRoute(path='/api/v1/scraping/story-info', name='scrape_story_info', methods=['POST'])
  File "/usr/local/lib/python3.11/site-packages/starlette/routing.py", line 76, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
          │                            │    │        │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x71fba2a56160>
          │                            │    │        │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fb923f00e0>
          │                            │    │        └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          │                            │    └ <starlette.requests.Request object at 0x71fb925a0f10>
          │                            └ <function request_response.<locals>.app.<locals>.app at 0x71fb92393f60>
          └ <function wrap_app_handling_exceptions at 0x71fba64e1f80>
  File "/usr/local/lib/python3.11/site-packages/starlette/_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "/usr/local/lib/python3.11/site-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x71fb923936a0>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x71fb923f00e0>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('**********', 8000), 'c...
          └ <function request_response.<locals>.app.<locals>.app at 0x71fb92393f60>
  File "/usr/local/lib/python3.11/site-packages/starlette/routing.py", line 73, in app
    response = await f(request)
                     │ └ <starlette.requests.Request object at 0x71fb925a0f10>
                     └ <function get_request_handler.<locals>.app at 0x71fba2ae80e0>
  File "/usr/local/lib/python3.11/site-packages/fastapi/routing.py", line 301, in app
    raw_response = await run_endpoint_function(
                         └ <function run_endpoint_function at 0x71fba7ecb600>
  File "/usr/local/lib/python3.11/site-packages/fastapi/routing.py", line 212, in run_endpoint_function
    return await dependant.call(**values)
                 │         │      └ {'request': StoryInfoRequest(story_url=HttpUrl('https://webtruyen.diendantruyen.com/truyen/vo-dao-truong-sinh-ta-tu-hanh-co-k...
                 │         └ <function scrape_story_info at 0x71fba317eb60>
                 └ Dependant(path_params=[], query_params=[], header_params=[], cookie_params=[], body_params=[ModelField(field_info=Body(Pydant...

  File "/app/API/routers/scraping.py", line 459, in scrape_story_info
    return await controller.scrape_story_info(request, background_tasks)
                 │          │                 │        └ <fastapi.background.BackgroundTasks object at 0x71fb923c1d10>
                 │          │                 └ StoryInfoRequest(story_url=HttpUrl('https://webtruyen.diendantruyen.com/truyen/vo-dao-truong-sinh-ta-tu-hanh-co-kinh-nghiem')...
                 │          └ <function ScrapingController.scrape_story_info at 0x71fba4f02480>
                 └ <routers.scraping.ScrapingController object at 0x71fba32e89d0>

  File "/app/API/routers/scraping.py", line 108, in scrape_story_info
    raise ScrapingError(f"Failed to scrape story information: {e}")
          └ <class 'middleware.error_handling.ScrapingError'>

middleware.error_handling.ScrapingError: Failed to scrape story information: Scraping service initialization failed: BrowserType.launch: Executable doesn't exist at /home/<USER>/.cache/ms-playwright/chromium_headless_shell-1179/chrome-linux/headless_shell
╔════════════════════════════════════════════════════════════╗
║ Looks like Playwright was just installed or updated.       ║
║ Please run the following command to download new browsers: ║
║                                                            ║
║     playwright install                                     ║
║                                                            ║
║ <3 Playwright Team                                         ║
╚════════════════════════════════════════════════════════════╝
