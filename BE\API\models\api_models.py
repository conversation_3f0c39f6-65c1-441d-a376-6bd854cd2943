"""
API Request and Response Models

This module defines Pydantic models for API requests and responses,
providing validation, serialization, and documentation for all endpoints.
"""

from datetime import datetime
from typing import Optional, List, Dict, Any, Union
from pydantic import BaseModel, Field, HttpUrl, validator
from enum import Enum

from .database import PyObjectId, StoryStatus, ScrapingStatus, EnhancementStatus


# ============================================================================
# Common Response Models
# ============================================================================

class APIResponse(BaseModel):
    """Base API response model"""
    success: bool
    message: str
    timestamp: datetime = Field(default_factory=datetime.utcnow)


class PaginationInfo(BaseModel):
    """Pagination information"""
    page: int = 1
    page_size: int = 20
    total_items: int
    total_pages: int
    has_next: bool
    has_previous: bool


class PaginatedResponse(APIResponse):
    """Paginated response base"""
    pagination: PaginationInfo
    data: List[Any]


# ============================================================================
# Story Scraping API Models
# ============================================================================

class StoryInfoRequest(BaseModel):
    """Request to scrape story information"""
    story_url: HttpUrl
    max_pages: int = Field(default=10, ge=1, le=100, description="Maximum number of pages to scrape for chapters")

    @validator('story_url')
    def validate_story_url(cls, v):
        url_str = str(v)
        if 'webtruyen.diendantruyen.com' not in url_str:
            raise ValueError('Only webtruyen.diendantruyen.com URLs are supported')
        return v


class ChapterInfo(BaseModel):
    """Chapter information from scraping"""
    chapter_number: int
    title: str
    url: HttpUrl


class StoryInfoResponse(APIResponse):
    """Response from story information scraping"""
    story_id: Optional[str] = None
    title: str
    author: Optional[str] = None
    description: Optional[str] = None
    cover_image_url: Optional[HttpUrl] = None
    status: StoryStatus
    total_chapters: int
    chapters: List[ChapterInfo] = Field(default_factory=list)
    scraping_job_id: Optional[str] = None


class BatchChapterScrapeRequest(BaseModel):
    """Request to scrape multiple chapters"""
    chapter_urls: List[HttpUrl]
    story_id: Optional[str] = None
    max_concurrent: int = Field(default=3, ge=1, le=10)
    rate_limit_delay: float = Field(default=2.0, ge=0.5, le=10.0)
    
    @validator('chapter_urls')
    def validate_chapter_urls(cls, v):
        if len(v) == 0:
            raise ValueError('At least one chapter URL is required')
        if len(v) > 100:
            raise ValueError('Maximum 100 chapters per batch')
        return v


class ChapterContent(BaseModel):
    """Chapter content from scraping"""
    chapter_number: int
    title: str
    url: HttpUrl
    content: str
    word_count: int
    is_locked: bool = False
    scraping_duration: Optional[float] = None


class BatchChapterScrapeResponse(APIResponse):
    """Response from batch chapter scraping"""
    job_id: str
    total_chapters: int
    chapters: List[ChapterContent] = Field(default_factory=list)
    failed_urls: List[str] = Field(default_factory=list)


# ============================================================================
# AI Enhancement API Models
# ============================================================================

class ChapterEnhancementRequest(BaseModel):
    """Request to enhance chapter content"""
    chapter_id: str
    force_re_enhance: bool = False


class BatchEnhancementRequest(BaseModel):
    """Request to enhance multiple chapters"""
    chapter_ids: Optional[List[str]] = None
    story_id: Optional[str] = None  # Enhance all chapters of a story
    force_re_enhance: bool = False
    batch_size: int = Field(default=1, ge=1, le=5)
    
    @validator('chapter_ids', 'story_id')
    def validate_input(cls, v, values):
        chapter_ids = values.get('chapter_ids')
        story_id = values.get('story_id')
        
        if not chapter_ids and not story_id:
            raise ValueError('Either chapter_ids or story_id must be provided')
        if chapter_ids and story_id:
            raise ValueError('Provide either chapter_ids or story_id, not both')
        return v


class EnhancementResult(BaseModel):
    """Single chapter enhancement result"""
    chapter_id: str
    original_length: int
    enhanced_length: int
    improvement_notes: List[str] = Field(default_factory=list)
    enhancement_duration: Optional[float] = None
    quality_score: Optional[float] = None


class BatchEnhancementResponse(APIResponse):
    """Response from batch enhancement"""
    job_id: str
    total_chapters: int
    completed_chapters: int = 0
    results: List[EnhancementResult] = Field(default_factory=list)
    failed_chapters: List[str] = Field(default_factory=list)


# ============================================================================
# Data Retrieval API Models
# ============================================================================

class StoryListRequest(BaseModel):
    """Request for story list with filtering"""
    page: int = Field(default=1, ge=1)
    page_size: int = Field(default=20, ge=1, le=100)
    search: Optional[str] = None
    author: Optional[str] = None
    status: Optional[StoryStatus] = None
    genre: Optional[str] = None
    scraping_status: Optional[ScrapingStatus] = None
    sort_by: str = Field(default="created_at", pattern="^(created_at|updated_at|title|total_chapters)$")
    sort_order: str = Field(default="desc", pattern="^(asc|desc)$")


class StoryListItem(BaseModel):
    """Story item in list response"""
    id: str
    title: str
    author: Optional[str] = None
    status: StoryStatus
    total_chapters_scraped: int
    total_chapters_enhanced: int
    enhancement_progress: float
    cover_image_url: Optional[HttpUrl] = None
    created_at: datetime
    updated_at: datetime


class StoryListResponse(PaginatedResponse):
    """Response for story list"""
    data: List[StoryListItem]


class StoryDetailsResponse(APIResponse):
    """Response for story details"""
    id: str
    title: str
    url: HttpUrl
    author: Optional[str] = None
    description: Optional[str] = None
    genres: List[str] = Field(default_factory=list)
    tags: List[str] = Field(default_factory=list)
    status: StoryStatus
    cover_image_url: Optional[HttpUrl] = None
    total_chapters_scraped: int
    total_chapters_enhanced: int
    enhancement_progress: float
    scraping_status: ScrapingStatus
    created_at: datetime
    updated_at: datetime
    last_scraped_at: Optional[datetime] = None


class ChapterListRequest(BaseModel):
    """Request for chapter list"""
    story_id: str
    page: int = Field(default=1, ge=1)
    page_size: int = Field(default=50, ge=1, le=200)
    enhanced_only: bool = False
    scraped_only: bool = False


class ChapterListItem(BaseModel):
    """Chapter item in list response"""
    id: str
    chapter_number: int
    title: str
    url: HttpUrl
    is_scraped: bool
    is_enhanced: bool
    enhancement_status: EnhancementStatus
    word_count: Optional[int] = None
    created_at: datetime
    updated_at: datetime


class ChapterListResponse(PaginatedResponse):
    """Response for chapter list"""
    data: List[ChapterListItem]


class ChapterContentResponse(APIResponse):
    """Response for chapter content"""
    id: str
    story_id: str
    chapter_number: int
    title: str
    url: HttpUrl
    original_content: Optional[str] = None
    enhanced_content: Optional[str] = None
    is_enhanced: bool
    enhancement_status: EnhancementStatus
    word_count: Optional[int] = None
    enhancement_metadata: Optional[Dict[str, Any]] = None
    created_at: datetime
    updated_at: datetime


# ============================================================================
# Job Status API Models
# ============================================================================

class JobStatusResponse(APIResponse):
    """Response for job status"""
    job_id: str
    job_type: str
    status: ScrapingStatus
    progress_percentage: float
    total_items: Optional[int] = None
    completed_items: int
    failed_items: int
    errors: List[str] = Field(default_factory=list)
    created_at: datetime
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    estimated_completion: Optional[datetime] = None


# ============================================================================
# Search and Filter API Models
# ============================================================================

class SearchRequest(BaseModel):
    """Advanced search request"""
    query: str = Field(min_length=1)
    search_in: List[str] = Field(default=["title", "author", "description"])
    page: int = Field(default=1, ge=1)
    page_size: int = Field(default=20, ge=1, le=100)
    filters: Dict[str, Any] = Field(default_factory=dict)
    
    @validator('search_in')
    def validate_search_fields(cls, v):
        valid_fields = ["title", "author", "description", "content"]
        for field in v:
            if field not in valid_fields:
                raise ValueError(f'Invalid search field: {field}')
        return v


class ContentComparisonRequest(BaseModel):
    """Request to compare original and enhanced content"""
    chapter_id: str
    comparison_type: str = Field(default="side_by_side", pattern="^(side_by_side|diff|statistics)$")


class ContentComparisonResponse(APIResponse):
    """Response for content comparison"""
    chapter_id: str
    chapter_title: str
    original_length: int
    enhanced_length: int
    similarity_score: Optional[float] = None
    improvement_notes: List[str] = Field(default_factory=list)
    comparison_data: Dict[str, Any] = Field(default_factory=dict)


# ============================================================================
# Export API Models
# ============================================================================

class ExportRequest(BaseModel):
    """Request to export story/chapter data"""
    story_ids: Optional[List[str]] = None
    chapter_ids: Optional[List[str]] = None
    export_format: str = Field(default="json", pattern="^(json|csv|epub|txt)$")
    include_enhanced: bool = True
    include_original: bool = False
    include_metadata: bool = True


class ExportResponse(APIResponse):
    """Response for export request"""
    export_id: str
    download_url: str
    file_size: Optional[int] = None
    expires_at: datetime
